@{
  Certificate = @{
    ThumbPrint      = '58AB88C9F51ABA5816CFAE40F08BDC858840C444'
    Path            = "\\intra.camosun.bc.ca\groups\sysadmins\Documentation\Certificates\GoDaddy\camosun.ca (WILDCARD) (unlimited server license)\2025\_.camosun.ca\camosun.ca.2025.openssl341.pfx"
    FriendlyName    = 'Camosun Wildcard Certificate'
    Location        = 'LocalMachine'
    Store           = 'WebHosting'
    DestinationPath = 'Cert:\LocalMachine\WebHosting\'
  }
  Prod        = @{
    HostNames = @(
      'labcollector.camosun.ca'
      'mylabaccess.camosun.ca'
    )
  }
  Dev         = @{
    HostNames = @(
      'labcollectordev.camosun.ca'
      'mylabaccessdev.camosun.ca'
    )
  }
}
