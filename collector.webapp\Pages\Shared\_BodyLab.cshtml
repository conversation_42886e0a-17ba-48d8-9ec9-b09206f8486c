@using collector.common
@using collector.common.model
@model LabDataModel

<!-- Start of Body -->
<section id="content">
  <!-- section content -->
  <div class="row">
    <!-- div row -->
    <div class="span4">
      <!-- div span4 -->
      @if (@Model.lab.IsAdminOnly == true || @Model.lab.IsStaffOnly == true)
      {
        <h3>@Model.lab.RoomName</h3>
      }
      else
      {
        <h3>@Model.lab.Campus, @Model.lab.Building : @Model.lab.RoomName</h3>
      }
      <p>Last Update: @Model.lab.LastUpdated.ToLocalTime().ToString("ddd MMM dd, yyyy h:mm tt")</p>
      <table class="table">
        <thead>
          <tr>
            <th scope="col">Computer ID</th>
            <th scope="col">Status</th>
            <th scope="col">Action</th>
          </tr>
        </thead>
        <tbody>
          @if (@Model.clients.Count() > 0)
          {
            @* This doesn't work when clients are not connected *@
            @foreach (var client in @Model.clients)
            {
              var ra = RowAction.GetAction(client);
              @* <!-- Set <tr class="free"> or <tr class="busy"> --> *@
              <tr class="@ra.Class">
                <td>@client.HostName</td>
                <td>@ra.Use</td>
                <td>
                  @if (client.CurrentState == DeviceState.Available)
                  {
                    @* See OnPostRDPAsync method in Model *@
                    <form asp-page-handler="RDP" asp-route-DNSHostName="@client.DNSHostName" method="POST">
                      <button class="@ra.Button">Login</button>
                      @* <input type="hidden" name="DNSHostName" value="@client.DNSHostName"> *@
                    </form>
                  }
                  else
                  {
                    <a href="@ra.RDP_URI" class="@ra.Button">Login</a>
                  }
                </td>
              </tr>
              @* <!-- Page/table loop code end --> *@
            }
          }
          else
          {
            <tr>
              <td> No clients registered </td>
            </tr>
          }
        </tbody>
      </table>
    </div> <!-- div span 4 -->
    <div class="span8">
      <!-- span8 -->
      @if (@Model.lab.HasSchedule == true)
      {
        <iframe style="width:100%;height:100%;"
        src="https://@Collector.CalendarServer/lab-calendar/week-view.php?room=@Model.lab.RoomName"></iframe>
      }
    </div> <!-- span8 -->
  </div> <!-- div row -->
</section> <!-- section content -->
<!-- End of Body -->
