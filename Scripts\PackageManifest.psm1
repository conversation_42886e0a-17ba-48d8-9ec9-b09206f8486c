<#
  .NOTES

  .DESCRIPTION
    PackageManifest is a json file containing the package version
    and required files & hashes. It is used to check for changes
    to the required files.

    Static methods are available to:
    - Generate a manifest for a given directory
    - Read an existing manifest
    - Write a manifest

    Class methods include:
    - Incrementing manifest version

    Additional Support classes
    - ManifestChanges (for comparing manifests)
    - FileHash (For handling file hashes)
#>

#region ManifestChanges
class ManifestChanges {
  [bool]$IsChanged = $false
  [bool]$VersionMatches = $false
  [bool]$PackageMatches = $false

  # Manifests
  [PackageManifest]$Source
  [PackageManifest]$Destination

  # These are a list of keys (names)
  # keyed: source = destination
  [hashtable]$Adds
  # just destinations
  [string[]]$Removes
  # keyed: source = destination
  [hashtable]$Updates
}
#endregion ManifestChanges

#region FileHash
class FileHash {
  [string]$Hash
  [string]$File

  # Transform from Get-FileHash for Select-Object
  static [hashtable]$Transform = @{
    Property = @(
      'Hash'
      @{
        Name       = 'File'
        Expression = {
          # Get a relative path and remove leading .\
          ($_ | Resolve-Path -Relative) -replace '^\.\\'
        }
      }
    )
  }

  static [hashtable] ToHashtable(
    [FileHash[]]$Items
  ) {
    $temp = @{}

    $Items.ForEach(
      { $temp[$_.File] = $_.Hash }
    )

    return $temp
  }

  static [hashtable] FromJsonObj(
    [System.Object]$Data
  ) {
    $memberNames = $Data |
      Get-Member -MemberType NoteProperty |
      Select-Object -ExpandProperty Name

    $temp = @{}

    foreach ($n in $memberNames) {
      $temp[$n] = $Data.$n
    }

    return $temp
  }
}
#endregion FileHash

#region VersionParams
enum VersionParams {
  Major
  Minor
  Build
}
#endregion VersionParams

#region PackageManifest
class PackageManifest {
  # required for MongoDB
  hidden [object]$_id
  hidden [string]$Directory

  [string]$Name
  [bool]$Latest
  [bool]$FromList = $false
  [bool]$Exists = $false
  # PS5.1 doesn't support SemVer
  [string]$Version = '0.0.0'
  [hashtable]$Hashes

  #region IncrementVersion
  [void] IncrementVersion() {
    # Convert version string to version object
    $temp_version = [version]($this.Version)

    $temp_version = [version]::new(
      $temp_version.Major,
      $temp_version.Minor,
      $temp_version.Build + 1
    )

    # Convert back to string for easy JSON serialization
    $this.Version = $temp_version.ToString()
  }
  #endregion IncrementVersion

  #region IncrementVersion VP
  [void] IncrementVersion(
    [VersionParams]$vp
  ) {
    # Convert version string to version object
    $temp_version = [version]($this.Version)

    $temp_version = switch ($vp) {
      ([VersionParams]::Major) {
        [version]::new(
          $temp_version.Major + 1,
          $temp_version.Minor,
          $temp_version.Build
        )
      }
      ([VersionParams]::Minor) {
        [version]::new(
          $temp_version.Major,
          $temp_version.Minor + 1,
          $temp_version.Build
        )
      }
      default {
        [version]::new(
          $temp_version.Major,
          $temp_version.Minor,
          $temp_version.Build + 1
        )
      }
    }

    # Convert back to string for easy JSON serialization
    $this.Version = $temp_version.ToString()
  }
  #endregion IncrementVersion VP

  static [string]$ManifestFilename = 'manifest.json'

  #region Compare
  static [ManifestChanges]Compare(
    [PackageManifest]$Next,
    [PackageManifest]$Previous
  ) {
    # TODO: Need to add version checking?
    $Changes = [ManifestChanges]@{
      Source      = $Next
      Destination = $Previous
      Adds        = @{}
      Updates     = @{}
    }

    if ($Next.Name -ne $Previous.Name) {
      Write-Host ("--- Next [{0}] name doesn't match Previous [{1}]!!" -f @(
          $Next.Name
          $Previous.Name
        ))

      $Changes.PackageMatches = $false
    }

    if ([version]$Next.Version -lt [version]$Previous.Version) {
      Write-Host ('--- Next [{0}:{1}] version is less than Previous [{2}:{3}]!!' -f @(
          $Next.Name
          $Next.Version
          $Previous.Name
          $Previous.Version
        ))

      $Changes.VersionMatches = $false
    } else {
      Write-Host ('--- [{0}:{1}] -> [{2}:{3}]' -f @(
          $Next.Name
          $Next.Version
          $Previous.Name
          $Previous.Version
        ))
    }

    if ([version]$Next.Version -eq [version]$Previous.Version) {
      $Changes.VersionMatches = $true
    }

    # Handle Manifests from lists
    # We need to flatten the names for comparison, but then provide the original for changes
    if ($Next.FromList) {
      # Lists are used to consolidate various published files into one directory as a package.
      # So if Next has something like Scripts/SomeFile.ps1 = <HASH>
      # Then Previous might have SomeFile.ps1 = <HASH>
      $FlatNext = @{}

      Push-Location $Next.Directory

      foreach ($Key in $Next.Hashes.Keys) {
        # Get the item
        $Item = Get-Item -Path $Key

        # Index based on the item's Name, which might match an index in Previous.Hashes
        # and map it against the original for hash comparisons.
        $FlatNext[$Item.Name] = $Key
      }

      Pop-Location

      # Compare both for Adds, and translate
      # TODO: Fix this, sync isn't working correctly.
      $FlatNext.Keys.Where(
        { !$Previous.Hashes.ContainsKey($_) }
      ).ForEach(
        # Source = Destination
        # Resolves to Next keys to add
        { $Changes.Adds[$FlatNext[$_]] = $_ }
      )

      # Compare for Removes
      $Changes.Removes, $BothKeys = @(
        $Previous.Hashes.Keys.Where(
          { !$FlatNext.ContainsKey($_) },
          'Split'
        )
      )

      # Compare for Updates
      $BothKeys.Where(
        # Do the lookup for comparison of hashes
        { $Next.Hashes[$FlatNext[$_]] -ne $Previous.Hashes[$_] }
      ).ForEach(
        # Source = Destination
        # Resolves to Next keys to add
        { $Changes.Updates[$FlatNext[$_]] = $_ }
      )
    } else {
      # Compare both for Adds
      $Next.Hashes.Keys.Where(
        { !$Previous.Hashes.ContainsKey($_) }
      ).ForEach(
        { $Changes.Adds[$_] = $_ }
      )

      # Compare for Removes
      $Changes.Removes, $BothKeys = @(
        $Previous.Hashes.Keys.Where(
          { !$Next.Hashes.ContainsKey($_) },
          'Split'
        )
      )

      # Compare for Updates
      $BothKeys.Where(
        { $Next.Hashes[$_] -ne $Previous.Hashes[$_] }
      ).ForEach(
        { $Changes.Updates[$_] = $_ }
      )
    }

    # If there's changes,
    $Changes.IsChanged = ($Changes.Adds.Count -ne 0) -or
      ($Changes.Removes.Count -ne 0) -or
      ($Changes.Updates.Count -ne 0) -or
      ([Version]$Next.Version -gt [Version]$Previous.Version)

    return $Changes
  }
  #endregion Compare

  #region Generate
  static [PackageManifest]Generate(
    # DirectoryInfo?
    [System.IO.DirectoryInfo]$Path,
    [string[]]$Exclusions
  ) {
    # We don't handle an existing manifest file, but path should be a directory
    if (!(Test-Path -Path $Path -PathType Container)) {
      throw "PackageManifest::NotADirectory[$Path]"
    }

    # We're generating manifest data for existing filesystem
    # This should be able to run under PWSH 5.1
    # Build splats
    $Items = @{
      Recurse = $true
      File    = $true
      # Exclude = [PackageManifest]::ManifestFilename
      Exclude = $Exclusions
    }

    $Transform = ([FileHash]::Transform)
    # Process the files
    Push-Location -Path $Path

    $package_hashes = Get-ChildItem @Items |
      Get-FileHash |
      Select-Object @Transform

    $manifest = [PackageManifest]@{
      Name      = $Path.Name
      Directory = $Path.FullName
      Latest    = $true
      Exists    = ($package_hashes.File -contains [PackageManifest]::ManifestFilename)
      Hashes    = [FileHash]::ToHashtable( $package_hashes )
    }

    Pop-Location

    return $manifest
  }
  #endregion Generate

  #region GenerateFromList
  static [PackageManifest] GenerateFromList(
    [string]$Name,
    [string]$Version,
    [System.IO.DirectoryInfo]$RootDirectory,
    [System.IO.FileInfo[]]$FileList
  ) {
    $Transform = ([FileHash]::Transform)

    Push-Location -Path $RootDirectory

    $package_hashes = $FileList |
      Get-FileHash |
      Select-Object @Transform

    $manifest = [PackageManifest]@{
      Name      = $Name
      Version   = $Version
      Directory = $RootDirectory.FullName
      Latest    = $true
      FromList  = $true
      Exists    = ($package_hashes.File -contains [PackageManifest]::ManifestFilename)
      Hashes    = [FileHash]::ToHashtable( $package_hashes )
    }

    Pop-Location

    return $manifest
  }
  #endregion GenerateFromList

  #region Read
  <#
    .NOTES
      We want this to return a PackageManifest for a manifest.json file.
      This takes a FileInfo object (Get-Item ...), so it should already
      exist.
  #>
  static [PackageManifest]Read(
    # FileInfo.
    [System.IO.FileInfo]$Path
  ) {
    # Get the manifest as a FileInfo object
    if (
      $Path.Name -ne ([PackageManifest]::ManifestFilename)
    ) {
      throw "PackageManifest::InvalidNameException[$($Path.FullName)]"
    }

    $data = Get-Content -Path $Path |
      ConvertFrom-Json

    return [PackageManifest]@{
      Version   = $data.Version
      # PowerShell 5.1 doesn't have an -AsHashTable method for json
      # So we have to do this by hand. )-:
      Hashes    = [FileHash]::FromJsonObj($data.Hashes)
      Directory = $Path.Directory.FullName
      Latest    = $data.Latest
      Name      = $data.Name
      Exists    = $true
    }
  }
  #endregion Read

  #region Write
  static [void]Write(
    [PackageManifest]$Manifest,
    [System.IO.DirectoryInfo]$Path
  ) {
    if (!(Test-Path -Path $Path -PathType Container)) {
      throw "PackageManifest::NotADirectory[$Path]"
    }

    $DestPath = Join-Path -Path $Path.FullName -ChildPath ([PackageManifest]::ManifestFilename)

    $JsonOptions = @{
      Compress = $true
    }

    $FileOptions = @{
      FilePath = $DestPath
      Encoding = 'Utf8'
      Force    = $true
    }

    $Manifest |
      ConvertTo-Json @JsonOptions |
      Out-File @FileOptions
    Write-Host "--- Wrote $DestPath"
  }
  #endregion Write
}
#endregion PackageManifest

#region File Functions
#region New-Manifest
function New-Manifest {
  [CmdletBinding()]
  param (
    # Source Folder
    [Parameter(Mandatory)]
    [string]
    $Path,
    # Manifest Version
    [Parameter(Mandatory)]
    [version]
    $Version,
    # Exclusions
    [string[]]$Exclusions
  )
  if (Test-Path -Path $Path -PathType Container) {
    $pkg_manifest = [PackageManifest]::Generate($Path, $Exclusions)

    $pkg_manifest.Version = $Version.ToString()

    [PackageManifest]::Write($pkg_manifest, $Path)
  } else {
    throw "PackageManifest::NotADirectory[$Path]"
  }
}
#endregion New-Manifest

#region Get-Manifest
function Get-Manifest {
  [CmdletBinding()]
  param(
    # Source Manifest Folder or File
    [Parameter(Mandatory, ParameterSetName = 'Directory')]
    [Parameter(Mandatory, ParameterSetName = 'FileList')]
    [string]
    $Path,
    # Exclusions
    [Parameter(ParameterSetName = 'Directory')]
    [string[]]
    $Exclusions,
    # Get manifest for a list of files
    [Parameter(Mandatory, ParameterSetName = 'FileList')]
    [string[]]
    $Files,
    # Package Name
    [Parameter(Mandatory, ParameterSetName = 'FileList')]
    [string]
    $Name,
    # Package Version
    [Parameter(Mandatory, ParameterSetName = 'FileList')]
    [version]
    $Version
  )

  switch ($PSCmdlet.ParameterSetName) {
    'Directory' {
      $Directory = $null
      # If it's a directory, append manifest default file name to the path
      if (Test-Path -Path $Path -PathType Container) {
        Write-Verbose "[$Path] is a directory"

        $Directory = Get-Item -Path $Path

        $Path = (Join-Path -Path $Path -ChildPath ([PackageManifest]::ManifestFilename))
      }

      if (Test-Path -Path $Path -PathType Leaf) {
        Write-Verbose "[$Path] is a file"

        $Item = Get-Item -Path $Path

        # Returns the read manifest
        [PackageManifest]::Read($Item)
      } else {
        # Do we want to throw an error?
        # throw "PackageManifest::ItemNotFound[$Path]"

        # Or return an empty PackageManifest, because this needs to be used for
        # comparing? Maybe we just generate one instead?
        [PackageManifest]::Generate($Directory.FullName, $Exclusions)
      }
    }
    'FileList' {
      if (Test-Path -Path $Path -PathType Container) {
        Write-Verbose '[FileList]'
        Write-Verbose "Name: [$Name]"
        Write-Verbose "Version: [$Version]"

        # Get the DirectoryInfo object
        $Location = Get-Item -Path $Path
        Write-Verbose "Path: [$($Location.GetType()) : $($Location.FullName)]"

        # Get the FileInfo objects
        $CheckFiles = @{}

        $Files.ForEach(
          { $CheckFiles[$_] = (Test-Path -Path $_ -PathType Leaf) }
        )

        $report = $CheckFiles |
          Format-Table -AutoSize |
          Out-String

        if ($CheckFiles.Values -notcontains $false) {
          $Items = Get-Item -Path $Files
        } else {

          throw "PackageManifest::InvalidFileSupplied[$report]"
        }
        Write-Verbose "Name: [$($Items.FullName)]"
        Write-Verbose "Report: [$($report.Trim())]"

        [PackageManifest]::GenerateFromList(
          $Name,
          $Version,
          $Location,
          $Items
        )
      } else {
        throw "PackageManifest::NotADirectory[$Path]"
      }
    }
    Default {}
  }
}
#endregion Get-Manifest

#region Get-ManifestChanges
function Get-ManifestChanges {
  [CmdletBinding()]
  param (
    # Source Manifest Data
    [Parameter(Mandatory)]
    [PackageManifest]
    $SrcManifest,
    # Destination Manifest Data
    [Parameter(Mandatory)]
    [PackageManifest]
    $DstManifest
  )

  [PackageManifest]::Compare($SrcManifest, $DstManifest)
}
#endregion Get-ManifestChanges

#region Set-Manifest
function Set-Manifest {
  [CmdletBinding()]
  param (
    [Parameter(Mandatory)]
    [PackageManifest]
    $Manifest,
    # Destination
    [Parameter(Mandatory)]
    [ValidateScript({ Test-Path -PathType Container -Path $_ })]
    [string]
    $Destination
  )

  [PackageManifest]::Write($Manifest, $Destination)
}
#endregion Set-Manifest

#region Sync-Folder
function Sync-Folder {
  [CmdletBinding(
    SupportsShouldProcess
  )]
  param (
    # Source Folder
    [Parameter(ParameterSetName = 'Folder')]
    [ValidateScript({ Test-Path -PathType Container -Path $_ })]
    [string]
    $Source,
    # Destination Folder
    [Parameter(ParameterSetName = 'Folder')]
    [ValidateScript({ Test-Path -PathType Container -Path $_ })]
    [string]
    $Destination,
    # Sync Using manifest Changes
    [Parameter(ParameterSetName = 'ManifestChanges')]
    [ManifestChanges]
    $ManifestChanges
  )
  try {
    # Initialize for PS5.1
    [PackageManifest]$SrcManifest
    [PackageManifest]$DstManifest
    [hashtable]$SrcPath = @{}
    [hashtable]$DstPath = @{}

    if ($PSCmdlet.ParameterSetName -eq 'Folder') {
      # Get existing? manifests
      $SrcManifest = Get-Manifest -Path $Source
      $DstManifest = Get-Manifest -Path $Destination

      # Compute the changes here
      $ManifestChanges = Get-ManifestChanges -Source $SrcManifest -Destination $DstManifest
    } elseif ($PSCmdlet.ParameterSetName -eq 'ManifestChanges') {
      # Pull the manifests directly from the Changes
      $SrcManifest = $ManifestChanges.Source
      $DstManifest = $ManifestChanges.Destination
    }

    $SrcPath['Path'] = $SrcManifest.Directory
    $DstPath['Path'] = $DstManifest.Directory

    if ($ManifestChanges.IsChanged) {
      # Adds (new items)
      foreach ($Key in $ManifestChanges.Adds.Keys) {
        $SourceChild = @{ ChildPath = $Key }
        $DestChild = @{ ChildPath = $ManifestChanges.Adds[$Key] }

        $CopyOptions = @{
          Path        = (Join-Path @SrcPath @SourceChild)
          Destination = (Join-Path @DstPath @DestChild)
          Force       = $true
          # Verbose     = $true
          # WhatIf      = $true
        }
        Write-Host ('  + [{0}] -> [{1}]' -f $CopyOptions['Path'], $CopyOptions['Destination'])

        # If there's a nested directory, it needs to be created first.
        $TargetDir = Split-Path -Path $CopyOptions['Destination']

        if(!(Test-Path -PathType Container -Path $TargetDir)){
          New-Item -ItemType Directory -Path $TargetDir -Force -Verbose > $null
        }

        Copy-Item @CopyOptions
      }

      # Updates to existing items
      foreach ($Key in $ManifestChanges.Updates.Keys) {
        $SourceChild = @{ ChildPath = $Key }
        $DestChild = @{ ChildPath = $ManifestChanges.Updates[$Key] }

        $CopyOptions = @{
          Path        = (Join-Path @SrcPath @SourceChild)
          Destination = (Join-Path @DstPath @DestChild)
          Force       = $true
          # Verbose     = $true
          # WhatIf      = $true
        }
        Write-Host ('  ^ [{0}] -> [{1}]' -f $CopyOptions['Path'], $CopyOptions['Destination'])

        Copy-Item @CopyOptions
      }

      # Removing previous package items
      foreach ($item in $ManifestChanges.Removes) {
        $ChildPath = @{ ChildPath = $item }
        # Remove
        $RemoveOptions = @{
          Path  = (Join-Path @DestPath @ChildPath)
          Force = $true
          # Verbose = $true
          # WhatIf  = $true
        }
        Write-Host ('  x [{0}] -> [ x ]' -f $RemoveOptions['Path'])

        Remove-Item @RemoveOptions
      }

      # Need to handle the list manifests differently
      if ($SrcManifest.FromList) {
        Push-Location -Path $SrcManifest.Directory
        $updatedHashes = @{}

        foreach ($Key in $SrcManifest.Hashes.Keys) {
          $Item = Get-Item -Path $Key

          $updatedHashes[$Item.Name] = $SrcManifest.Hashes[$Key]
          Write-Verbose "[$Key -> $($Item.Name) : $($SrcManifest.Hashes[$Key])]"
        }
        Pop-Location

        $SrcManifest.Hashes = $updatedHashes
      }

      # Finally write the source manifest to the destination
      Set-Manifest -Manifest $SrcManifest -Destination $DstManifest.Directory
    } else {
      Write-Host "--- No Changes [$($DstManifest.Name)]"
    }
  } catch {
    throw $_
  }
}
#endregion Sync-Folder
#endregion File Functions
