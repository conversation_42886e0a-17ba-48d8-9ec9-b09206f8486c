#Requires -Module ActiveDirectory
# Purpose: To create a webpage for available computers in labs
# Author: <PERSON><PERSON>
# Date: 2020-03-18
# History
#   2020-03-18: Initialized code
#   2020-03-19: changed user name to student
#   2020-03-19: added ICS and Mech labs
#   2020-03-19: pulled out non-essential information
#   2020-03-20: added error handler and log handler
#   2020-03-24: added "In-Use" and changed "Available for Use" from "Online"
#   2020-03-25: added function and edit css
#               added civil webpage
#   2020-03-26: added convert htmldecode function and edit css
#   2020-03-31: removed unused variables and added libraries
#   2020-04-01: removed Get-Bodyinfo function and created New-RDPSessionTable and Get-RDPSessionTable
#               fixed to keep showing disconnected users as In-Use
#   2020-04-02: fixed unable to show local logon
#   2020-04-03: fixed glitch and empty cells
[CmdletBinding()]
param (
  [Parameter()]
  [switch]
  $RunOnce,
  # Parameter help description
  [Parameter()]
  [switch]
  $Transcript,
  # Parameter help description
  [Parameter()]
  [switch]
  $ShowJson,
  # Parameter help description
  [Parameter()]
  [int]
  $MaxRuntimeSeconds = 20,
  # Stop Time is default to 12:00am [00:00] in the morning.
  [Parameter()]
  [datetime]
  $StopTime = (Get-Date '00:00'),
  # Start Time is default to 7:00am [07:00] in the morning.
  [Parameter()]
  [datetime]
  $StartTime = (Get-Date '06:55'),
  # Data File
  [Parameter(ParameterSetName = 'Start', Mandatory)]
  [Parameter(ParameterSetName = 'Run', Mandatory)]
  [Parameter(ParameterSetName = 'Stop', Mandatory)]
  [Parameter(ParameterSetName = 'StopWithGuid', Mandatory)]
  [Parameter(ParameterSetName = 'Setup', Mandatory)]
  [String]
  $DataFile, #= "$PSScriptRoot\LabData.psd1",
  # Parameter help description
  [Parameter(ParameterSetName = 'Start', Mandatory)]
  [switch]
  $Start,
  # Parameter help description
  [Parameter(ParameterSetName = 'Setup', Mandatory)]
  [Parameter(ParameterSetName = 'Test', Mandatory)]
  [Parameter(ParameterSetName = 'Start')]
  [PSCredential]
  $Credentials,
  # Parameter help description
  [Parameter(ParameterSetName = 'Stop', Mandatory)]
  [Parameter(ParameterSetName = 'StopWithGuid' , Mandatory)]
  [switch]
  $Stop,
  # Lab Data GUID
  [Parameter(ParameterSetName = 'Run', Mandatory)]
  [Parameter(ParameterSetName = 'StopWithGuid', Mandatory)]
  [string]
  $GUID,
  # CMS Module Path
  [Parameter(ParameterSetName = 'Setup')]
  [Parameter(ParameterSetName = 'Start')]
  [string]
  $CMS_Module = "$PSScriptRoot\CMS.psm1",
  # # Default Credentials
  # [Parameter(ParameterSetName = 'Setup')]
  # [Parameter(ParameterSetName = 'Start')]
  # [string]
  # $CMS_File,
  # Switch for Setup Mode
  [Parameter(ParameterSetName = 'Setup', Mandatory)]
  [switch]
  $Setup,
  # Switch for Remove Mode
  [Parameter(ParameterSetName = 'Remove', Mandatory)]
  [Parameter(ParameterSetName = 'Test')]
  [switch]
  $Remove,
  # Test Setup
  [Parameter(ParameterSetName = 'Test', Mandatory)]
  [switch]
  $Test
)

# Functions
function JobExists ($taskname) {
  #$task = Get-ScheduledTask -TaskName $taskname -ErrorAction SilentlyContinue
  $task = Get-ScheduledJob -ErrorAction SilentlyContinue -Name $taskname
  $exists = $?
  return $exists, $task
}

function CheckComputers {
  <#
    .SYNOPSIS
    Checks a list of computers and provides an array of HashTables.

    .DESCRIPTION
    Takes an array of computer names and generates an array of HashTables.
    The Hashtables are populated via several fast functions:
    - FastTC : Fast Test Connection
    - FastQUser: Fast Quser lookups
    - FastDNS : Fast DNS lookup

    Each function uses the begin - process - end blocks in order to leverage
    PowerShell parallelism.

    .EXAMPLE
    $computers = @(list, of, computer, names, ...)
    $Systems = CheckComputers -Computers $computers

    .NOTES
    Returns an array of Hashtables where each element:
    @{
      ComputerName = ""
      WinRM = ""
      Online = ""
      DNSName = ""
    }

    #>
  [CmdletBinding()]
  param(
    [Parameter(ParameterSetName = 'Update')]
    [hashtable[]]$ComputerHashes,
    [Parameter(ParameterSetName = 'New')]
    [string[]]$Computers
  )
  # Set the error action preference
  $ErrorActionPreference = 'SilentlyContinue'
  $WarningPreference = 'SilentlyContinue'
  function FastTC {
    param ( )
    begin { }
    process {
      $tc = @{
        ComputerName = $_.ComputerName
        BufferSize   = 16
        Count        = 1
        Quiet        = $true
      }
      if (Test-Connection @tc) {
        $_['Online'] = $true
        #$_.Add('Online' , $true)
      } else {
        $_['Online'] = $false
        #$_.Add('Online' , $false)
      }
    }
    end { }
  }

  function FastTNC {
    param ( )
    begin { }
    process {
      $tnc = @{
        ComputerName     = $_.DNSName
        InformationLevel = 'Quiet'
      }
      if (Test-NetConnection @tnc) {
        $_['Online'] = $true
        #$_.Add('Online' , $true)
      } else {
        $_['Online'] = $false
        #$_.Add('Online' , $false)
      }
    }
    end { }
  }

  function FastWSMan {
    param ( )
    begin { }
    process {
      if (Test-WSMan -ComputerName $_.ComputerName) {
        $_['WinRM'] = $true
        #$_.Add('WinRM', $true)
      } else {
        $_['WinRM'] = $false
        #$_.Add('WinRM' , $false)
      }
    }
    end { }
  }

  function FastDNS {
    param ( )
    begin { }
    process {
      $dns_name = Resolve-DnsName -Name $_.ComputerName | Select-Object -First 1
      $_['DNSName'] = $dns_name.Name
      #$_.Add('DNSName' , $dns_name.Name)
      $_['IPAddress'] = $dns_name.IPAddress
      #$_.Add('IPAddress', $dns_name.IPAddress)
    }
    end { }
  }
  if ($PSCmdlet.ParameterSetName -eq 'New') {
    # Convert Array of names to an array of hashes
    $ComputerHashes = $Computers.ForEach( {
        @{
          ComputerName = $_
          #DNSName = "[$null|"<SomeName>"]"
          #Online = "[$true|$false]"
        }
      })
  }
  # Pings
  $ComputerHashes | FastTC
  # NSLookup
  $ComputerHashes | FastDNS

  return $ComputerHashes
}

$FastQUserScriptblock = {
  function FastQUser {
    param ( )
    begin { }
    process {
      # First, get the users from quser
      $quser = query user 2>$null

      if ($quser) {
        # If we have users
        # Generate the header for the hashtable
        $qheader = ($quser[0] -split '\s\s+').Trim()
        # Split the text into the user sessions
        $users = $quser[1..$($quser.Count)]
        # Generate the array of users
        $UsersArray = @(
          foreach ($line in $users) {
            # Handle disconnected sessions
            if ($line -match 'Disc') {
              # need to replace empty session name with something. -- in this case
              $line = $line -replace '^(\s\w+\s\s)', '$1--'
            }
            # Split up the user lines with a marker and convert to hashtable
            $user = $line -replace '\s\s+', ';' |
              ConvertFrom-String -Delimiter ';' -PropertyNames $qheader

            # Return the hastable as an element of the array
            $user
          }
        )
        # Add this to the computer hash.
        $_['Users'] = $UsersArray
        #$_.Add("Users", $UsersArray)
      } else {
        # No user sessions found.
        <## >
        $Empty = @(
          @{
            USERNAME     = "-"
            SESSIONNAME  = "-"
            ID           = "-"
            STATE        = "-"
            "IDLE TIME"  = "-"
            "LOGON TIME" = "-"
          }
        )#>
        # Put "None" for Users instead.
        $_['Users'] = 'None'
        #$_.Add("Users", "None")
      }
    }
    end { }
  }

  # Run the function locally
  $ThisHost = @{ }
  try {
    # Run the function locally
    $ThisHost | FastQUser
    # And return a hashtable.
  } catch {
    Write-Error -Message $Env:COMPUTERNAME
    Throw $_
  } finally {
    if ($ThisHost.Count -eq 0 ) {
      $ThisHost['Users'] = 'None'
      $ThisHost['Online'] = $false
    }
  }
  return $ThisHost
}

filter ObjectToHashTable ([string[]]$properties) {
  $in = $_
  # $properties = $in.psobject.properties.Name
  $out = @{}
  foreach ($p in $properties) {
    $out[$p] = $in.$p
  }
  $out
}

function New-RDPFile {
  param (
    #[string]$ComputerName,
    [string]$FolderPath,
    [switch]$UseMongoDB
  )
  begin { }
  process {
    if ($UseMongoDB) {
      # Generate some variables
      $Name = $_.HostName
      $RDPFile = "$FolderPath\$Name.rdp"

      # Generate file content
      $RDPValues = @(
        "full address:s:$($_.DNSHostName)"
        'audiocapturemode:i:1'
        'audiomode:i:0'
        'dynamic resolution:i:1'
        #"domain:s:CAMOSUN"
      )
    } else {
      # Generate some variables
      $Name = $_.ComputerName
      $RDPFile = "$FolderPath\$Name.rdp"

      # Generate file content
      $RDPValues = @(
        "full address:s:$($_.DNSName)"
        'audiocapturemode:i:1'
        'audiomode:i:0'
        'dynamic resolution:i:1'
        #"domain:s:CAMOSUN"
      )

      # Set the URI for the ComputerHash
      $_['RDP_URI'] = "$Name.rdp"
    }
    # Generate FileSplat
    $FileSplat = @{
      Path     = $RDPFile
      Value    = $RDPValues
      Encoding = 'utf8'
      Force    = $true
    }
    # Check to see if the file exists
    if (Test-Path -Path $RDPFile -PathType Leaf) {
      # If it does, get the content
      $gc = Get-Content $RDPFile
      $diff = Compare-Object -ReferenceObject $gc -DifferenceObject $RDPValues
      # Check the first line (TODO fix this!!)
      #if ($gc[0].GetHashCode() -ne $RDPValues[0].GetHashCode()) {
      if ($null -ne $diff) {
        # Replace it if it's not correct
        Set-Content @FileSplat
      }
    } else {
      # If the file doesn't exist, make it correctly.
      Set-Content @FileSplat
    }
    # Set the URI for the ComputerHash
    # $_['RDP_URI'] = "$Name.rdp"
    #$_.Add("RDP_URI", "$Name.rdp")

  }
  end { }
}

<##>
## Reference Start
##
function New-TableBlock {
  param (
    [string]$Label,
    [string]$LastUpdated,
    [string]$TableBody,
    [string]$Name
  )
  $TableBlock = @"
        <!-- Page/table loop code begin -->
        <h3>$Label</h3>
        <!-- Use this format: ddd MMM dd, yyyy h:mm tt -->
        <p>Last update: $LastUpdated</p>
        <table class="table">
          <thead>
            <tr>
              <th scope="col">Computer ID</th>
              <th scope="col">Status</th>
              <th scope="col">Action</th>
            </tr>
          </thead>
          <tbody>
            $TableBody
          </tbody>
        </table>
        </div>
        <div style="height: 500px;margin-top: 50px;" class="span8">
          <iframe style="width:100%;height:100%;" src="https://webservices3.camosun.bc.ca/lab-calendar/week-view.php?room=$Name"></iframe>
        </div>
        <!-- Page/table loop code end -->
"@
  return $TableBlock
}

##
## Reference End
#>

function New-TableRow {
  param(
    [string]$ComputerName,
    [switch]$IsOnline,
    [switch]$IsFree,
    [string]$RDP_URI
  )
  $Action = if ($IsOnline) {
    if ($IsFree) {
      @{
        Class   = 'free'
        Use     = 'Available'
        RDP_URI = "$RDP_URI"
        Button  = 'btn btn-success'
      }
    } else {
      @{
        Class   = 'busy'
        Use     = 'In use'
        RDP_URI = '#'
        Button  = 'btn disabled'
      }
    }
  } else {
    @{
      Class   = 'busy'
      Use     = 'Unavailable'
      RDP_URI = '#'
      Button  = 'btn disabled'
    }
  }

  $TableRow = @"
<!-- Set <tr class="free"> or <tr class="busy"> -->
<tr class="$($Action.Class)">
  <td>$ComputerName</td>
  <td>$($Action.Use)</td>
  <!-- Set <a class="btn btn-success"> or <a class="btn disabled"> -->
  <td><a href="$($Action.RDP_URI)" class="$($Action.Button)">Log in</a></td>
</tr>
"@
  return $TableRow
}

function GenerateComputerListXML {
  param (
    [string]$SamAccountName,
    [string]$GUID,
    [string]$XMLDataFile #= "$PSScriptRoot\XML\$SamAccountName-$GUID.xml"
  )

  if (!(Test-Path -Path "$PSScriptRoot\XML" -PathType Container)) {
    New-Item -Path "$PSScriptRoot\XML" -ItemType Directory -Force
  }

  Write-Host ('--- Generating: [ {0} : {1} : {2} ] ---' -f $SamAccountName, $GUID, $XMLDataFile )

  $computers = Get-ADGroupMember -Identity $SamAccountName |
    Where-Object objectClass -EQ 'computer' |
    Sort-Object Name |
    Select-Object -ExpandProperty Name

  Write-Host ('---  Computers: [ {0} ]' -f $computers.Count)

  Export-Clixml -Path $XMLDataFile -Force -Encoding utf8 -InputObject $computers
}
function GenerateComputerInfo {
  [CmdletBinding()]
  param (
    [string]$SamAccountName,
    [string]$GUID,
    [string]$XMLDataFile,
    [switch]$UseMongoDB
    #[bool]$Update = $false
  )

  <## >
  #begin {
  if (!(Test-Path -Path "$PSScriptRoot\XML" -PathType Container)) {
    New-Item -Path "$PSScriptRoot\XML" -ItemType Directory -Force
  }

  $XMLDataFile = "$PSScriptRoot\XML\$SamAccountName-$GUID.xml"
  if (Test-Path -Path $XMLDataFile -PathType Leaf) {
    $ComputerData = Import-Clixml -Path $XMLDataFile
    $Update = $true
  }
  #}
  #>

  #process {
  try {
    # Generate a new hashtable
    if ($UseMongoDB) {
      # Connect to the local MongoDB Database
      Connect-Mdbc -ConnectionString . -DatabaseName collectordb

      # Get the list of Collections
      $Collections = Get-MdbcCollection

      # $LabData = $Collections.Where({ $_.CollectionNamespace.CollectionName -eq 'LabData' }) | Select-Object

      # Get a handle to the ClientData Collection
      $ClientDataCollection = $Collections.Where({ $_.CollectionNamespace.CollectionName -eq 'ClientData' }) | Select-Object
      Write-Host ('--- Collecting: [ {0} : {1} ] ---' -f $ClientDataCollection.CollectionNamespace.CollectionName, $GUID )

      # Filter all of the lab clients from the collection as Client objects
      $ComputerData = get-mdbcData -Filter @{ Lab_GUID = [guid]$GUID } -As Client -Collection $ClientDataCollection

      Write-Host ('---  Computers: [ {0} ]' -f $ComputerData.Count)
      # # The rest of the script expects an array of hashes, so we need to convert this data
      # # Get the list of the class properties
      # $ClientProperties = @{ properties = [client]::new().psobject.properties.Name }

      # # Filter the data, constructing hashtables for each object
      # $ComputerData = @(
      #   $ClientData | ObjectToHashTable @ClientProperties
      # )

      # Generate RDP files using this data
      $ComputerData | New-RDPFile -FolderPath $PageFolder -UseMongoDB

    } else {
      # Import the list of computers
      $computers = Import-Clixml -Path $XMLDataFile
      # Process things the old way
      $ComputerData = CheckComputers -Computers $computers

      <## >
    # Get the list from AD
    $computers = Get-ADGroupMember -Identity $SamAccountName |
      Sort-Object Name |
      Select-Object -ExpandProperty Name
    # Check all of the desired computers
    if ($Update) {
      # Generate a new Array with removed entries excluded
      $ComputerData = $ComputerData.Where({ $computers -contains $_.ComputerName })
      # Generate a new Array with new computer elements.
      $ComputerData = $ComputerData + $computers.Where({
          $ComputerData.ComputerName -notcontains $_
        }).ForEach({
          @{
            ComputerName = $_
          }
        })
      # Check the list for updates
      $ComputerData = CheckComputers -ComputerHashes $ComputerData
    } else {
      # Generate a new hashtable
      $ComputerData = CheckComputers -Computers $computers
    }#>

      # Start Jobs for each Online Computer in the HashArray
      $ComputerData.Where( {
          $_.Online -eq $true
        }).ForEach( {
          $ic = @{
            JobName      = "QUser-$SamAccountName-$($_['ComputerName'])"
            ComputerName = "$($_['ComputerName'])"
            ScriptBlock  = $FastQUserScriptblock
          }
          # Start the job, but suppress unneeded output
          Invoke-Command @ic > $null
        })

      # Process the jobs
      do {
        # Get the newest QUser Job
        #$Job = Get-Job -Name "QUser-$SamAccountName-*" -Newest 1
        #foreach ( $Job in $Jobs){
        # Get the Jobs
        $Jobs = Get-Job -Name "QUser-$SamAccountName-*"
        # Delta shift for  groups of devices >= '$MaxRuntimeSeconds' devices; increases stopping time
        #   Runtime Diff == ($Job.PSBeginTime - delta) - ($MaxRuntimeSeconds - delta)
        #                => $Job.PSBeginTime - $MaxRuntimeSeconds - 2delta
        #                => $Job.PSBeginTime - ($MaxRuntimeSeconds + 2delta)
        # Where:
        #   delta = $Jobs.Count / $MaxRuntimeSeconds => Jobs/Second ?
        $delta = [int](2 * ($Jobs.Count / $MaxRuntimeSeconds))
        <#
      ### JOB STATES ###
      ### https://docs.microsoft.com/en-us/dotnet/api/system.management.automation.jobstate?view=powershellsdk-1.1.0 ###
        AtBreakpoint 10  Script execution is halted in a debugger stop.
        Blocked       5  Command execution is blocked (on user input host calls etc)
        Completed     2  Execution of command completed in all computernames/runspaces
        Disconnected  7  The job is a remote job and has been disconnected from the server.
        Failed        3  An error was encountered when trying to executed command in one or more computernames/runspaces
        NotStarted    0  Execution of command in job not started
        Running       1  Execution of command in progress
        Stopped       4  Command execution is cancelled (stopped) in one or more computernames/runspaces.
        Stopping      9  Stop is in progress
        Suspended     6  The job has been suspended
        Suspending    8  Suspend is in progress

      ### State handlers:
      SomeState -> AtBreakpoint -> SomeState (=> Stopping) : We don't care about breakpoints in jobs, so stop them
      SomeState -> Blocked -> SomeState (=> Stopping) : We don't get user input in these jobs
      SomeState -> Completed => Receive-Job : This Job ran correctly.
      SomeState -> Disconnected -> SomeState (=> Stopping) : We're not using PSSessions directly, so stop the job?
      SomeState -> Failed => Remove-Job : This job failed, so we set the system as Offline
      Start-Job -> NotStarted => Ignored : This job is waiting to start, ignore
      SomeState -> Running => Stop-job if over MaxTime, otherwise continue
      Stopping -> Stopped => Remove-Job : This job was stopped, remove it.
      SomeState -> Stopping -> Stopped : This job has changed to an undesired state and will be stopped.
      Suspending -> Suspended => Resume-Job : This job was suspended for some unknown reason and will be resumed.
      SomeState -> Suspending -> Suspended : Something has caused this job to suspend.
      #>
        switch ($Jobs) {
          # Completed Jobs are received and removed
          { $_.State -eq 'Completed' } {
            $Job = $_
            $ComputerData.Where( {
                $_['ComputerName'] -eq $Job.Location
              }).ForEach( {
                # Return the data from the Job object and remove it at the same time
                $NewData = Receive-Job -Job $Job -Wait -AutoRemoveJob
                # Set the new data
                foreach ($Key in $NewData.Keys) {
                  $_[$Key] = $NewData[$Key]
                  #$_.Add($Key, $NewData[$Key])
                }
              })
            continue
          }
          # Running Jobs are checkd for duration
          # Those over max runtime are stopped.
          { $_.State -eq 'Running' } {
            # next element
            $RunTime = (New-TimeSpan -Start $_.PSBeginTime -End (Get-Date)).TotalSeconds
            if ($RunTime -ge ($MaxRuntimeSeconds + $delta)) {
              Stop-Job -Job $_
            } <#else {
            Start-Sleep -Milliseconds 100
            continue
            #Start-Sleep -Milliseconds 100
          }#>
          }
          # These are undetermined states, stop them
          { @(
              'Disconnected'
              'AtBreakpoint'
              'Blocked'
            ) -contains $_.State
          } {
            Stop-Job -Job $_
            continue
          }
          # Suspended state jobs should be resumed
          'Suspended' {
            Resume-Job -Job $_
            continue
          }
          # These are transient States, ignore them
          { @(
              'Stopping'
              'Suspending'
              'NotStarted'
            ) -contains $_.State
          } {
            continue
          }
          # These are stopped and failed jobs, remove them.
          { @(
              'Stopped'
              'Failed'
            ) -contains $_.State
          } {
            $Job = $_
            # Need to handle making these systems unavailable.
            Write-Host ('--- Job [{0}] has [{1}] after [{2:n3}s] ' -f $Job.Name, $Job.State, (New-TimeSpan -Start $Job.PSBeginTime -End $Job.PSEndTime).TotalSeconds )
            # Needs Testing....
            $ComputerData.Where( {
                $_.ComputerName -eq $Job.Location
              }).ForEach( {
                $_['Online'] = $false
              })
            # Remove the job.
            Remove-Job -Job $Job
            continue
          }
          # No more jobs
          # This only works because we have accounted for all job states.
          Default { }
        }
        # wait a bit before doing the next batch
        Start-Sleep -Milliseconds 100
        #}
        #$Jobs = Get-Job | Sort-Object State
      } until ($null -eq $Jobs)

      # Generate the RDP files for the lab.
      $ComputerData.Where( { $_['Online'] -eq $true }) | New-RDPFile -FolderPath $PageFolder
    }

  } catch {
    throw $_
    exit
  }
  #}

  #end {
  #Export-Clixml -Path $XMLDataFile -Force -Encoding utf8 -Depth 10 -InputObject $ComputerData
  #}
  return $ComputerData
}
function Update-LabHtml {
  param (
    [string]$Label,
    [string]$PageFolder,
    [string]$OutputFile,
    [string]$SamAccountName,
    [string]$GUID,
    [string]$Name,
    [switch]$UseMongoDB
  )
  # Initialize some data.
  $HtmlHeader = Get-Content "$PSScriptRoot\index_header.txt"
  $HtmlFooter = Get-Content "$PSScriptRoot\index_footer.txt"

  # Get all computers in specific OU or Security group.
  try {

    # Check that the PageFolder exists.
    if (!(Test-Path -Path $PageFolder -PathType Container)) {
      New-Item -Path $PageFolder -ItemType Directory -Force
    }

    # Get the TimeStamp (Done before scanning)
    $TimeStamp = Get-Date -Format 'ddd MMM dd, yyyy h:mm tt'

    # Generate Computer Data
    $GCI_Info = @{
      SamAccountName = $SamAccountName
      GUID           = $GUID
      XMLDataFile    = "$PSScriptRoot\XML\$SamAccountName-$GUID.xml"
      UseMongoDB     = $UseMongoDB
    }
    $ComputersHashArray = GenerateComputerInfo @GCI_Info

    # Show JSON output for debugging.
    if ($ShowJson) {
      $ComputersHashArray | ConvertTo-Json -Depth 5
    }
    # Use the Computer Data to generate the correct html documents.
    # Generate the TableBody TableRows for each computer
    $TableBody = @(
      $ComputersHashArray.foreach({
          if ($UseMongoDB) {
            $NewRow = @{
              ComputerName = $_.HostName
              IsOnline     = ($_.CurrentState -in 'Available', 'InUse')
              IsFree       = ($_.CurrentState -in 'Available')
              RDP_URI      = ('{0}.rdp' -f $_.HostName)
            }
          } else {
            $NewRow = @{
              ComputerName = $_.ComputerName
              IsOnline     = $_.Online
            }

            if ($_.Online) {
              $IsFree = if ($_.Users -eq 'None') {
                $true
                # Logic bug in test, -ne returns 'Disc' even though Active is present
                # NotContains ensures there is no active connection.
              } elseif ($_.Users.State -notcontains 'Active') {
                $true
              } else {
                $false
              }
              $NewRow['IsFree'] = $IsFree
              $NewRow['RDP_URI'] = $_.RDP_URI
            }
          }
          # Returns a string for the array.
          New-TableRow @NewRow
        })
    )

    $HtmlTable = New-TableBlock -Label $Label -Name $Name -TableBody $TableBody -LastUpdated $TimeStamp

    $OutputHtml = @( $HtmlHeader, $HtmlTable, $HtmlFooter ) #| Out-String

    $OutputHtml | Out-File -FilePath $OutputFile -Force -Encoding utf8
  } catch {
    throw $_
    return
  }
}
<##>

try {
  if ($Transcript) {
    $dow = Get-Date -Format 'dddd'

    if (!(Test-Path -Path "$PSScriptRoot\Logs" -PathType Container)) {
      New-Item -Path "$PSScriptRoot\Logs\$dow" -ItemType Directory -Force
    }
    $TranscriptFile = if($null -ne $GUID){
      @{ Path = "$PSScriptRoot\Logs\$dow\Transcript-$GUID.log" }
    } else {
      @{ Path = "$PSScriptRoot\Logs\$dow\Transcript-Check_RDPLogin.log" }
    }
    if (Test-Path @TranscriptFile -PathType Leaf) {

      $today = Get-Date -Format 'yyyyMMdd'
      $LastLogged = Get-Date -Format 'yyyyMMdd' (Get-ChildItem @TranscriptFile).LastWriteTime

      if ($today -eq $LastLogged) {
        #If we're still on the same day, append
        Start-Transcript @TranscriptFile -Append
      } else {
        # Otherwise start over.
        Start-Transcript @TranscriptFile
      }
    } else {
      # Otherwise start over.
      Start-Transcript @TranscriptFile
    }
  }
  switch ($PSCmdlet.ParameterSetName) {
    'Start' {
      Write-Host "--- [$_] ---"
      $LabData = Import-PowerShellDataFile -Path $DataFile
      # Use the cms file instead of the passed Credential object.
      # Import-Module $CMS_Module
      # $Credentials = if ( $null -ne $CMS_File ) {
      #   RetrieveCredentials -CMS_File $CMS_File
      # }

      if ($null -eq $Credentials ) {
        # throw 'NoCredentialFoundError'
        Import-Module 'Microsoft.PowerShell.SecretManagement', 'Microsoft.PowerShell.SecretStore'
        $Credentials = Get-Secret -Name 'LabCollectorAgent'
      }

      foreach ($Lab in $LabData.Labs) {

        $Command = "$PSCommandPath -GUID '$($Lab.GUID)' -Transcript -DataFile $DataFile" # -RunPath $RunPath"

        <# ScheduledTask #>
        $Name = "MyLabAccess-$($Lab.GUID)"
        # Action
        $NSTA = @{
          Execute          = 'powershell'
          Argument         = "-NoLogo -NonInteractive -NoProfile -Command `"& {$Command}`""
          WorkingDirectory = "$PSScriptRoot"
        }

        <# Principal # >
        $NSTP = @{
          UserId = $Credentials.UserName
        } #>

        <# Trigger, No Triggers for these. # >
        $NSTT = @{
          At   = (Get-Date)
          Once = $true
        } #>

        # Settings
        $NSTSS = @{
          RestartCount    = 5
          RestartInterval = (New-TimeSpan -Minutes 10)
        }

        $Registration = @{
          #InputObject = New-ScheduledTask @NST
          Description = 'MyLabAccess for {0} : {1}' -f $Lab.Department, ($Lab.LabNames -join ', ')
          Action      = New-ScheduledTaskAction @NSTA
          #Trigger     = New-ScheduledTaskTrigger @NSTT
          Settings    = New-ScheduledTaskSettingsSet @NSTSS
          TaskName    = $Name
          TaskPath    = '\'
          #Principal   = New-ScheduledTaskPrincipal @NSTP
          User        = $Credentials.UserName
          Password    = $Credentials.GetNetworkCredential().Password
          RunLevel    = 'Highest'
        }
        #>

        # ScheduledJob is messing with the threads. May need to convert this to just scheduledtasks
        $ScheduledTask = @{
          #TaskPath = "\Microsoft\Windows\PowerShell\ScheduledJobs\"
          TaskPath = '\'
          TaskName = "$($Registration.TaskName)"
        }
        $Task = Get-ScheduledTask @ScheduledTask -ErrorAction SilentlyContinue
        $Exists = $?
        if (!$Exists) {
          Register-ScheduledTask @Registration
          # Wait for registration
          Start-Sleep -Seconds (Get-Random $MaxRuntimeSeconds)
          $Task = Get-ScheduledTask @ScheduledTask -ErrorAction SilentlyContinue
        }
        # Start the Job if it's not running.
        Switch ($Task.State) {
          'Running' {
            Write-Host "--- Running : $($ScheduledTask.TaskName)" -ForegroundColor Green
          }
          'Ready' {
            Write-Host "--- Starting : $($ScheduledTask.TaskName)" -ForegroundColor Green
            Start-ScheduledTask @ScheduledTask
            # Wait a random interval before moving on...
            Start-Sleep -Seconds (Get-Random $MaxRuntimeSeconds)
          }
          Default {
            Write-Host "--- Unhandled State [$_] : $($ScheduledTask.TaskName)" -ForegroundColor Magenta
          }
        }
      }
      if ($RunOnce) {
        do {
          # Wait at least a minute before recieving jobs.
          Start-Sleep -Seconds 60
          $Now = Get-Date
        } until ($RunOnce -or ($Now -ge $StopTime))
        Get-Job -Name 'MyLabAccess-*' | Receive-Job -Wait -AutoRemoveJob
      }
    }
    'Run' {
      Write-Host "--- [$_] ---"
      $LabData = Import-PowerShellDataFile -Path $DataFile
      # Time check for starts on reboots:
      $Now = Get-Date
      # Restarted after hours
      # Note: StopTime [00:00H] happens before StartTime [07:00H], so we have to be careful with the logic as
      # it can only be evaluated against today.
      if (($Now -ge $StopTime) -and ($Now -lt $StartTime)) {
        Write-Host "--- Restarted : Between [$StopTime] -> [$Now] -> [$StartTime] => After hours, stopping." -ForegroundColor Green
        exit
      }
      if (($Now -ge $StartTime) -and ($Now -lt $StopTime.AddDays(1))) {
        Write-Host "--- Started : Between [$StartTime] -> [$Now] -> [$($StopTime.AddDays(1))] => During normal hours, continuing." -ForegroundColor Green
      }
      # Start a counter
      $LoopCount = 0

      Write-Host '--- Lab Data Start ---'
      $Lab = $LabData.Labs.Where( { $_.GUID -eq $GUID }) | Select-Object
      $LabString = $Lab | ConvertTo-Json -Depth 10 | Out-String
      Write-Host ('--- {0}' -f $LabString)
      Write-Host '--- Lab Data End   ---'

      Write-Host '--- Initialize Lab Data Start ---'

      if ($Lab['UseMongoDB'] -eq $true) {
        Write-Host ('--- Using MongoDB [{0}] ---' -f $Lab['UseMongoDB'] )
        # import the collector.common.ps1
        . "$PSscriptRoot\collector.common.ps1"
      } else {
        Write-Host ('--- Using MongoDB [{0}] ---' -f $Lab['UseMongoDB'] )
        # otherwise generate an xml list for each lab group
        if ($Lab.IsGroup) {
          $GeneratorSplat = @{
            SamAccountName = $Lab.SamAccountName
            GUID           = $GUID
            XMLDataFile    = ('{0}\XML\{1}-{2}.xml' -f $PSScriptRoot, $Lab.SamAccountName, $GUID)
          }
          GenerateComputerListXML @GeneratorSplat
        } else {
          # This should only run once, and generate one XML file.
          foreach ($Name in $Lab.LabNames) {
            $GeneratorSplat = @{
              SamAccountName = "$($Name)_devices"
              GUID           = $GUID
              XMLDataFile    = ('{0}\XML\{1}-{2}.xml' -f $PSScriptRoot, "$($Name)_devices", $GUID)
            }
            GenerateComputerListXML @GeneratorSplat
          }
        }
      }
      Write-Host '--- Initialize Lab Data End ---'

      Write-Host '--- Update Lab HTML Start ---'
      do {
        $LoopTime = Measure-Command {
          if ($Lab.IsGroup) {
            $Label = if ( $Lab.Names -eq '' ) {
              $Lab.Department
            } else {
              @( $Lab.Department, ($Lab.LabNames -join ', ') ) -join ': '
            }
            $LabSplat = @{
              Label          = $Label
              PageFolder     = ( $Lab.OutputFile | Select-String -Pattern '\\\\.+\\' ).Matches.Value
              OutputFile     = $Lab.OutputFile
              SamAccountName = $Lab.SamAccountName
              GUID           = $GUID
              Name           = $Label
              UseMongoDB     = $Lab['UseMongoDB']
            }
            Update-LabHtml @LabSplat
          } else {
            foreach ( $Name in $Lab.LabNames) {
              $LabSplat = @{
                Label          = @( $Lab.Department, $Name ) -join ': '
                PageFolder     = @( $LabData.ServerRoot, $Lab.SiteApp, $Lab.SiteRoot, $Name ) -join '\'
                OutputFile     = @( $LabData.ServerRoot, $Lab.SiteApp, $Lab.SiteRoot, $Name, 'index.html' ) -join '\'
                SamAccountName = "$($Name)_devices"
                GUID           = $GUID
                Name           = $Name
                UseMongoDB     = $Lab['UseMongoDB']
              }
              Update-LabHtml @LabSplat
            }
          }
        }
        # Log to transcript
        Write-Host ('--- Loop #[{0,16}]# : [|{2:hh}:{2:mm}:{2:ss}|] [|{1}|]' -f $LoopCount, (Get-Date), $LoopTime )
        $LoopCount ++
        # Delay time
        Start-Sleep 5
        $Now = Get-Date
      } until (
        $RunOnce -or
        (
          ($Now -ge $StopTime.AddDays(1)) -and
          ($Now -lt $StartTime.AddDays(1))
        )
      ) #>
      Write-Host '--- Update Lab HTML End ---'
    }
    'Stop' {
      Write-Host "--- [$_] ---"
      $LabData = Import-PowerShellDataFile -Path $DataFile
      # Needed to actually get the jobs
      #Import-Module PSScheduledJob
      foreach ($Lab in $LabData.Labs) {
        <# ScheduledTasks #>
        $Registration = @{
          TaskName = 'MyLabAccess-{0}' -f $Lab.GUID
          TaskPath = '\'
        }
        Write-Host ('--- Stopping : {0}' -f ($Registration | ConvertTo-Json) )

        $Task = Get-ScheduledTask @Registration -ErrorAction SilentlyContinue
        $Exists = $?
        if ($Exists) {
          Get-ScheduledTask @Registration | Stop-ScheduledTask
          Get-ScheduledTask @Registration | Unregister-ScheduledTask -Confirm:$false
        }
        #>
      }
    }
    'StopWithGuid' {
      Write-Host "--- [$_] ---"
      $LabData = Import-PowerShellDataFile -Path $DataFile
      # Needed to actually get the jobs
      #Import-Module PSScheduledJob
      $Lab = $LabData.Labs.Where( { $_.GUID -eq $GUID })
      if ($null -ne $Lab) {
        <# ScheduledTasks #>
        $Registration = @{
          TaskName = 'MyLabAccess-{0}' -f $Lab.GUID
          TaskPath = '\'
        }
        Write-Host ('--- Stopping : {0}' -f ($Registration | ConvertTo-Json) )

        $Task = Get-ScheduledTask @Registration -ErrorAction SilentlyContinue
        $Exists = $?
        if ($Exists) {
          Get-ScheduledTask @Registration | Stop-ScheduledTask
          Get-ScheduledTask @Registration | Unregister-ScheduledTask -Confirm:$false
        }
        #>
      }
    }
    'Test' {
      $Tasks = @(
        @{
          TaskName = 'MyLabAccess Start Lab Tasks'
          TaskPath = '\'
        }
        @{
          TaskName = 'MyLabAccess Stop Lab Tasks'
          TaskPath = '\'
        }
      )

      $TestResults = @(
        foreach ($ScheduledTask in $Tasks) {
          try {
            $FoundTask = Get-ScheduledTask @ScheduledTask -ErrorAction Stop

            # Return if they match
            # EG:  'camosun\agent_MyLabAccess' -match 'agent_MyLabAccess'
            $Credentials.UserName -match $FoundTask.Principal.UserId
            # if ($FoundTask.Principal.UserID -ne $Credentials.UserName) {
            #   $false
            # } else {
            #   $true
            # }
          } catch {
            $false
          }
        }
      )

      return !($Remove -in $TestResults)
    }
    'Setup' {
      # Setup the Start and Stop Scheduled Tasks
      Write-Host "--- [$_] ---"
      $LabData = Import-PowerShellDataFile -Path $DataFile

      ## Start Scheduled Task
      # Test for the CMS file, as it is required for Start to be automated
      # if (!(Test-Path -PathType Leaf -Path $CMS_File)) {
      #   throw "CMS_FileNotFoundError: Path [$CMS_File] not valid."
      # }

      # # Use the CMS file if Credential not specified
      # if ($null -eq $Credentials) {
      #   Import-Module $CMS_Module
      #   $Credentials = RetrieveCredentials -CMS_File $CMS_File
      # }
      if ($null -eq $Credentials ) {
        # throw 'NoCredentialFoundError'
        Import-Module 'Microsoft.PowerShell.SecretManagement', 'Microsoft.PowerShell.SecretStore'
        $Credentials = Get-Secret -Name 'LabCollectorAgent'
      }
      # Check for LabData Correctness
      # do {
      #   Write-Host "--- LabData file is [$DataFile]. Is this correct? " -NoNewline
      #   $Agree = (Read-Host 'Y/N?').ToUpper()
      # } until (@('Y', 'N') -contains $Agree )
      # Write-Host "--- [ $Agree ]"

      # switch ($Agree) {
      #   'Y' {
      Write-Host "--- LabData file [$DataFile] will be used."
      #   }
      #   'N' {
      #     $PSDs = Get-ChildItem -Path "$PSScriptRoot\*.psd1"
      #     Write-Host '--- Options are:'
      #     for ($i = 0; $i -lt $PSDs.Count; $i++) {
      #       Write-Host "    [ $i ] $($PSDs[$i].Name)"
      #     }
      #     Write-Host "--- Please select a value in [0 -> $($PSDs.Count - 1)] " -NoNewline
      #     do {
      #       [uint]$Index = Read-Host '#?'
      #     } until (($null -ne $Index) -and ($Index -ge 0) -and ($Index -lt $PSDs.Count))
      #     Write-Host "--- [ $Index ]"
      #     $DataFile = $PSDs[$Index].FullName
      #     Write-Host "--- LabData file is now [$DataFile]"
      #   }
      # }

      $StartCommand = "$PSCommandPath -Start -Transcript -Verbose -DataFile $DataFile"
      # $StartCommand = "$PSCommandPath -Start -CMS_File '$CMS_File' -Transcript -Verbose -DataFile $DataFile"

      $StartAction = @{
        Execute          = 'powershell'
        Argument         = "-NoLogo -NonInteractive -NoProfile -Command `"& {$StartCommand}`""
        WorkingDirectory = "$PSScriptRoot"
      }

      $StartDaily = @{
        Daily = $true
        At    = '07:00' # Should be after $StartTime
      }

      $StartOnBoot = @{
        AtStartup = $true
      }

      $StartRegistraton = @{
        Description = 'Runs the script in Start mode, which should restore any non-functioning jobs.'
        Action      = New-ScheduledTaskAction @StartAction
        Trigger     = @(
          (New-ScheduledTaskTrigger @StartDaily)
          (New-ScheduledTaskTrigger @StartOnBoot)
        )
        Settings    = New-ScheduledTaskSettingsSet
        TaskName    = 'MyLabAccess Start Lab Tasks'
        TaskPath    = '\'
        #Principal   = New-ScheduledTaskPrincipal @StartPrincipal
        User        = $Credentials.UserName
        Password    = $Credentials.GetNetworkCredential().Password
        RunLevel    = 'Highest'
      }

      # Register the start task
      Register-ScheduledTask @StartRegistraton

      ## Stop Scheduled Task
      $StopCommand = "$PSCommandPath -Stop -Transcript -Verbose -DataFile $DataFile"

      $StopAction = @{
        Execute          = 'powershell'
        Argument         = "-NoLogo -NonInteractive -NoProfile -Command `"& {$StopCommand}`""
        WorkingDirectory = "$PSScriptRoot"
      }

      $StopDaily = @{
        Daily = $true
        At    = '00:05' # Should be after $StopTime
      }

      $StopRegistration = @{
        Description = 'Runs the script in Stop mode.'
        Action      = New-ScheduledTaskAction @StopAction
        Trigger     = New-ScheduledTaskTrigger @StopDaily
        Settings    = New-ScheduledTaskSettingsSet
        TaskName    = 'MyLabAccess Stop Lab Tasks'
        TaskPath    = '\'
        #Principal   = New-ScheduledTaskPrincipal @StopPrincipal
        User        = $Credentials.UserName
        Password    = $Credentials.GetNetworkCredential().Password
        RunLevel    = 'Highest'
      }

      Register-ScheduledTask @StopRegistration

    }
    'Remove' {
      # Build the splats for the Start and Stop tasks
      $StartRegistraton = @{
        TaskName    = 'MyLabAccess Start Lab Tasks'
        TaskPath    = '\'
        #Confirm     = $false
        ErrorAction = 'Continue'
      }

      $StopRegistration = @{
        TaskName    = 'MyLabAccess Stop Lab Tasks'
        TaskPath    = '\'
        #Confirm     = $false
        ErrorAction = 'Continue'
      }

      # Run stop task so that it includes the correct datafile
      Start-ScheduledTask @StopRegistration

      # Wait until the stop task has completed.
      do {
        $StopTask = Get-ScheduledTask @StopRegistration
        Start-Sleep -Seconds 1
      } until ($StopTask.State -ne 'Running')
      #& $PSCommandPath -Stop -Transcript

      # Unregsiter Start task
      Unregister-ScheduledTask @StartRegistraton -Confirm:$false

      # Unregister Stop Task
      Unregister-ScheduledTask @StopRegistration -Confirm:$false

    }
    Default { }
  }
} catch {
  throw $_
}
if ($Transcript) {
  Stop-Transcript
}
# Infinite loop
