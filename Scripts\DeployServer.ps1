<#
  .NOTES
  Collector Server
    mongodb (DeployMSI)
    mongodb PSModule
    files (LabCollector/Server) [site]
    collector.server service
    Firewall Rule?
    proxycollector tasks (Setup) [credentials]
    checkrdp tasks (Setup) [credentials?]

  Collector Client GPO
    Firewall Rule for collector.client
    Immediate tasks (Setup, Remove)
    ?

  Collector Client DSC
    Files (LabCollector/Client)
    Registration (Depends on Collector. Server fully running)
    Setup
    Heartbeat LastRun before now
    Build LabCollector
    dotnet publish -c Release

  Generate LabData files for each site
    Copy LabCollector Server files
    Copy LabCollector Client files

  Collector might need a 'ping':'pong' function before attempting to start/connect/register

#>

[CmdletBinding()]
param (
  # Splat for DSC source Path
  [Parameter()]
  [hashtable]
  $DSCSourcePath = @{ Path = '\\naslan\DSCSources' },
  # Configuration Output Path
  [Parameter()]
  [hashtable]
  # Local output directory; Hashtable: single key [Path]
  $OutputPath = @{ Path = 'C:\Scripts' },
  # Splat for Output Path Root
  [Parameter()]
  [hashtable]
  $DSCOutputRoot = @{ Path = 'C:\Scripts\DSC\' },
  # Remove Switch
  # [Parameter(ParameterSetName='Remove')]
  [switch]
  $Remove,
  # Server operation Switch
  [Parameter(ParameterSetName = 'Server', Mandatory)]
  [switch]
  $Server,
  # Modules Only
  [Parameter(ParameterSetName = 'Server')]
  [Parameter(ParameterSetName = 'WebApp')]
  [ValidateSet('Features', 'Modules', 'Certificate', 'Scripts', 'Packages')]
  [string[]]
  $SetupTasks = @('Scripts'),
  # Site to serve
  [Parameter(ParameterSetName = 'Server', Mandatory)]
  [ValidateSet('None', 'Interurban', 'Lansdowne', 'Prod', 'Dev')]
  [string]
  $Site,
  # Credentials required for Proxy scripts
  [Parameter(ParameterSetName = 'Server')]
  [PSCredential]
  $ProxyCredentials, # = (Get-Secret 'LabCollectorAgent')
  # WebApp operation Swtich
  [Parameter(ParameterSetName = 'WebApp', Mandatory)]
  [switch]
  $WebApp,
  # WebApp Mode
  [Parameter(ParameterSetName = 'WebApp', Mandatory)]
  [ValidateSet('Prod', 'Dev')]
  [string]
  $ServerMode,
  # PFx Credentials
  [Parameter(ParameterSetName = 'WebApp', Mandatory)]
  [PSCredential]
  $PfxCreds,
  # Force
  [Parameter()]
  [switch]
  $Force
)

Import-Module -Name $PSScriptRoot/PackageManifest.psm1

#region CheckLCM
function CheckLCM {
  param(
    [switch]$Force
  )
  if ($Force) {
    Write-Host '--- Not checking LCM State'
    return
  }
  Write-Host '--- Checking LCM State'
  $waitCounter = 0
  do {
    $LCMState = (Get-DscLocalConfigurationManager).LCMState
    $NotIdle = $LCMState -ne 'Idle'
    if ($NotIdle) {
      $waitTime = (Get-Random -Minimum 15 -Maximum 60)
      Write-Host ('--- [{2}:{0,3}] Waiting [{1,3}] seconds' -f ++$waitCounter, $waitTime, $LCMState)
      Start-Sleep -Seconds $waitTime
    }
  } while ($NotIdle)
}
#endregion CheckLCM

try {
  #region Initialization
  # Get start time
  $StartTime = [datetime]::now

  # Ensure Output Path exists
  if (!(Test-Path @OutputPath -PathType Container)) {
    New-Item @OutputPath -ItemType Directory -Force
  }

  # Handle Transcript
  $TranscriptFile = @{
    Path = (Join-Path @OutputPath -ChildPath 'DeployServer-Transcript.log' )
  }

  if (Test-Path @TranscriptFile -PathType Leaf) {
    $today = Get-Date -Format 'yyyyMMdd'
    $LastLogged = Get-Date -Format 'yyyyMMdd' (Get-ChildItem @TranscriptFile).LastWriteTime

    if ($today -eq $LastLogged) {
      #If we're still on the same day, append
      Start-Transcript @TranscriptFile -Append
    } else {
      # Otherwise start over.
      Start-Transcript @TranscriptFile
    }
  } else {
    # Otherwise start over.
    Start-Transcript @TranscriptFile
  }

  # Log start time
  Write-Host "--- StartTime [$StartTime]"
  Write-Host "--- SetupTasks [$($SetupTasks -join ' ')]"

  $SourceName = if (
    ($ServerMode -eq 'Dev') -or
    ($Site -in 'Dev','None')
  ) {
    'LabCollectorDev'
  } else {
    'LabCollector'
  }

  $LabCollectorLocalPath = @{ Path = Join-Path @OutputPath -ChildPath 'LabCollector' }
  $LabCollectorSourcePath = @{ Path = Join-Path @DSCSourcePath -ChildPath $SourceName }

  # Set Ensure State for DSC's
  $Ensure = if ($Remove) {
    'Absent'
  } else {
    'Present'
  }
  #endregion Initialization

  switch ($PSCmdlet.ParameterSetName) {
    'Server' {
      # Tasks should be done in this order.
      $FixedOrder = @('Packages', 'Modules', 'Scripts')

      # Select the specified Setup tasks from the fixed order, in order.
      $SetupTasks = $FixedOrder | Where-Object { $SetupTasks -EQ $_ }
      Write-Host '--- Credentials Check'
      if ($null -eq $ProxyCredentials) {
        $ProxyCredentials = Get-Credential -Message 'Proxy Credentials Required for Server Mode.'
      }

      # Generate Path names
      $SubPath = @{ ChildPath = 'Server' }

      # Build DSC to do the rest
      $DestServerPath = @{ Path = Join-Path @LabCollectorLocalPath @SubPath }
      $SourceServerPath = @{ Path = Join-Path @LabCollectorSourcePath @SubPath }

      # If the directory doesn't exist, create it.
      if (!(Test-Path @DestServerPath -PathType Container)) {
        New-Item @DestServerPath -ItemType Directory -Verbose
      }

      # Check that WSMAN Max envelope size is set correctly for DSC?
      $mes = Get-Item -Path WSMan:\localhost\MaxEnvelopeSizeKb
      if($mes.Value -lt 1000) {
        $UpdateMaxSize = @{
          Name    = 'maxEnvelopeSize'
          Path    = 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WSMAN\Client'
          Value   = 8192
          Force   = $true
          Verbose = $true
        }
        Set-ItemProperty @UpdateMaxSize
      }

      if ($Ensure -eq 'Present') {
        switch ($SetupTasks) {
          'Packages' {
            Write-Host '--- [Packages] MongoDB Install'
            # first. Install MongoDB; This might restart.
            & '\\naslan\MSIDeploy\DeployMSIPackage.ps1' -PackageNames MongoDB -ForcedRestart -Verbose -Force:$Force
          }

          'Modules' {
            Write-Host '--- [Modules] Install: Please use SetupModules.ps1'
          }

          'Scripts' {
            Write-Host '--- [Scripts]'

            Configuration SetupProxyWrapper {
              param(
                [pscredential]$ProxyCredentials,
                [ValidateSet('Absent', 'Present')]
                [string]$Ensure,
                [hashtable]$DestServerPath,
                [hashtable]$SourceServerPath,
                [string]$Site
              )

              # Import-Module -Name $PSScriptRoot/PackageManifest.psm1
              Import-DscResource -ModuleName 'PSDesiredStateConfiguration'
              Import-DscResource -ModuleName 'NetworkingDsc' -ModuleVersion 9.1.0

              Node localhost {
                # Need to enable some RSAT components
                # This is going to require a Set-DscLocalConfigurationManager
                LocalConfigurationManager {
                  ConfigurationMode  = 'ApplyOnly'
                  RebootNodeIfNeeded = $true
                  ActionAfterReboot  = 'ContinueConfiguration'
                }

                # Enable Required Windows Features
                WindowsFeatureSet RSATFeatures {
                  Name   = @(
                    'RSAT-AD-PowerShell'
                  )
                  Ensure = $Ensure
                }

                # Copy all of the files to the local system for use.
                <# Not Working Correctly # >
                File ServerFiles {
                  DestinationPath = $DestServerPath['Path']
                  SourcePath      = $SourceServerPath['Path']
                  Checksum        = 'SHA-256'
                  Force           = $true
                  Recurse         = $true
                  Type            = 'Directory'
                  MatchSource     = $true
                  Ensure          = $Ensure
                  DependsOn       = '[WindowsFeatureSet]RSATFeatures'
                }#>

                File ServerDirectory {
                  DestinationPath = $DestServerPath['Path']
                  Force           = $true
                  Type            = 'Directory'
                  Ensure          = $Ensure
                  DependsOn       = '[WindowsFeatureSet]RSATFeatures'
                }

                # TODO: This is where we want to get the manifests.
                # And then use that to generate the list of files to copy?
                # Only generating Script DSC's for files that need to change.

                # We want the file DSC to be based off of the manifest data
                $Manifests = @{
                  SrcManifest = Get-Manifest @SourceServerPath
                  DstManifest = Get-Manifest @DestServerPath -Exclusions Logs,LabData
                }

                $Changes = Get-ManifestChanges @Manifests

                $ChangesDependsOn = @{}
                $ChangeItems = $Changes.Adds + $Changes.Updates

                # File resource Adds/Updates
                foreach ($Key in $ChangeItems.Keys) {
                  $state_name = '[File]ServerPresent_{0}' -f $Key
                  $Item = $ChangeItems[$Key]

                  File $state_name {
                    DestinationPath = (Join-Path @DestServerPath -ChildPath $Item)
                    SourcePath      = (Join-Path @SourceServerPath -ChildPath $Key)
                    Type            = 'File'
                    Checksum        = 'SHA-256'
                    Force           = $true
                    Ensure          = 'Present'
                    DependsOn       = '[File]ServerDirectory'
                  }

                  # Add dependency key to index
                  $ChangesDependsOn[$state_name] = $Item
                }

                # File resource Removes (Does not clear empty directories)
                foreach ($Item in $Changes.Removes) {
                  $state_name = '[File]ServerAbsent_{0}' -f $Item

                  File $state_name {
                    DestinationPath = (Join-Path @DestServerPath -ChildPath $Item)
                    Type            = 'File'
                    Force           = $true
                    Ensure          = 'Absent'
                    DependsOn       = '[File]ServerDirectory'
                  }

                  $ChangesDependsOn[$state_name] = $Item
                }

                Service CollectorServerService {
                  Name           = 'collector.server'
                  # Path           ='C:\Scripts\LabCollector\Server\collector.server.exe'
                  Path           = (Join-Path @DestServerPath -ChildPath 'collector.server.exe')
                  BuiltInAccount = 'NetworkService'
                  StartupType    = 'Automatic'
                  DisplayName    = 'LabCollector Server'
                  Description    = 'LabCollector Server is a ZeroMQ (0MQ) + dotnet-core service that records collected data in a MongoDB Database.'
                  State          = 'Running'
                  Ensure         = $Ensure
                  Dependencies   = @('mongodb')
                  DependsOn      = (
                    '[File]ServerDirectory',
                    ($ChangesDependsOn.Keys)
                  )
                }

                # Ensure the manifest is present, as it's not handled above
                $manifest_state = ("Manifest_{0}_{1}" -f $Manifests.SrcManifest.Name, $Manifests.SrcManifest.Version)
                File $manifest_state {
                    DestinationPath = (Join-Path @DestServerPath -ChildPath 'manifest.json')
                    SourcePath      = (Join-Path @SourceServerPath -ChildPath 'manifest.json')
                    Type            = 'File'
                    CheckSum        = 'SHA-256'
                    # MatchSource     = $true
                    Force           = $true
                    Ensure          = 'Present'
                    DependsOn       = '[Service]CollectorServerService'
                }

                # Setup Firewall Rules
                $RuleTypes = @(
                  foreach ($p in @('UDP', 'TCP')) {
                    foreach ($d in @('Inbound', 'Outbound')) {
                      [PSCustomObject]@{
                        Protocol  = $p
                        Direction = $d
                      }
                    }
                  }
                )

                foreach ($type in $RuleTypes) {
                  Firewall ('CollectorService{0}Allow{1}' -f $type.Direction, $type.Protocol) {
                    Name        = ('CollectorService{0}Allow{1}' -f $type.Direction, $type.Protocol)
                    Protocol    = ($type.Protocol)
                    DisplayName = ('Allow collector.server.exe {0}' -f $type.Direction)
                    Profile     = @('Domain', 'Private')
                    Program     = 'C:\Scripts\LabCollector\Server\collector.server.exe'
                    Enabled     = $true
                    Direction   = ($type.Direction)
                    Ensure      = $Ensure
                    DependsOn   = '[Service]CollectorServerService'
                  }
                }

                Group AddProxyUserToAdmin {
                  GroupName        = 'Administrators'
                  Ensure           = 'Present'
                  MembersToInclude = $ProxyCredentials.UserName
                  DependsOn        = '[Service]CollectorServerService'
                }
              }
            }

            $ProxyWrapperStates = @{
              OutputPath       = (Join-Path @DSCOutputRoot -ChildPath 'SetupProxyWrapper')
              ProxyCredentials = $ProxyCredentials
              Ensure           = $Ensure
              DestServerPath   = $DestServerPath
              SourceServerPath = $SourceServerPath
              Site             = $Site
            }

            Write-Host '--- Compile DSC [SetupProxyWrapper]'

            SetupProxyWrapper @ProxyWrapperStates

            $LCMOptions = @{
              Path  = $ProxyWrapperStates['OutputPath']
              Force = $true
            }

            Write-Host '--- Setting LCM for [SetupProxyWrapper]'

            Set-DscLocalConfigurationManager @LCMOptions

            $InvokeCfg = @{
              Path         = $ProxyWrapperStates['OutputPath']
              ComputerName = 'localhost'
              Wait         = $true
              Force        = $true
            }

            CheckLCM -Force:$Force

            Write-Host '--- Invoke DSC [SetupProxyWrapper]'

            Start-DscConfiguration @InvokeCfg

            # Process ProxyWrapper
            if (
              (Test-Path -Path 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -PathType Leaf) -and
              !(& 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -Test -Credentials $ProxyCredentials)
            ) {
              Write-Host '--- Setup [ProxyWrapper]'

              & 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -Setup -Credentials $ProxyCredentials
            } else {
              Write-Host '--- Present [ProxyWrapper]'
            }
            # Process CheckRDPLogin
            # if (
            #   (Test-Path -Path 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -PathType Leaf) -and
            #   !(& 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -Test -Credentials $ProxyCredentials)
            # ) {
            #   Write-Host "--- Setup [Check_RDPLogin : $Site]"
            #   $DataFile = (Join-Path @DestServerPath -ChildPath "$Site.psd1")

            #   & 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -Setup -Credentials $ProxyCredentials -DataFile $DataFile
            # } else {
            #   Write-Host "--- Present [Check_RDPLogin : $Site]"
            # }
          }

          Default {
            Write-Host "--- SetupType [$_] not defined for [$($PSCmdlet.ParameterSetName)]"
          }
        }
      } else {
        # Do things in reverse order when we're removing things.
        [array]::Reverse($SetupTasks)

        switch ($SetupTasks) {
          'Packages' {
            Write-Host '--- [Packages] MongoDB Remove'
            # last. Install MongoDB; This might restart.
            & '\\naslan\MSIDeploy\DeployMSIPackage.ps1' -PackageNames MongoDB -ForcedRestart -Verbose -Remove -Force:$Force
          }

          'Modules' {
            Write-Host '--- [Modules] Remove: Not being removed.'
          }

          'Scripts' {
            Write-Host '--- [Scripts]'

            # Process ProxyWrapper
            if (
              (Test-Path -Path 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -PathType Leaf) -and
              !(& 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -Test -Remove -Credentials $ProxyCredentials)
            ) {
              Write-Host '--- Remove [ProxyWrapper]'

              & 'C:\Scripts\LabCollector\Server\ProxyWrapper.ps1' -Remove
            } else {
              Write-Host '--- Absent [ProxyWrapper]'
            }
            # Process CheckRDPLogin
            # if (
            #   (Test-Path -Path 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -PathType Leaf) -and
            #   !(& 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -Test -Remove -Credentials $ProxyCredentials)
            # ) {
            #   Write-Host '--- Remove [Check_RDPLogin]'

            #   & 'C:\Scripts\LabCollector\Server\Check_RDPLogin.ps1' -Remove
            # } else {
            #   Write-Host '--- Absent [Check_RDPLogin]'
            # }

            Configuration SetupProxyWrapper {
              param(
                [pscredential]$ProxyCredentials,
                [ValidateSet('Absent', 'Present')]
                [string]$Ensure,
                [hashtable]$DestServerPath,
                [hashtable]$SourceServerPath,
                [string]$Site
              )

              Import-DscResource -ModuleName 'PSDesiredStateConfiguration'
              Import-DscResource -ModuleName 'NetworkingDsc' -ModuleVersion 9.1.0

              Node localhost {
                # Copy all of the files to the local system for use.
                File ServerFiles {
                  DestinationPath = $DestServerPath['Path']
                  SourcePath      = $SourceServerPath['Path']
                  Checksum        = 'SHA-256'
                  Force           = $true
                  Recurse         = $true
                  Type            = 'Directory'
                  MatchSource     = $true
                  Ensure          = $Ensure
                  DependsOn       = '[Service]CollectorServerService'
                }

                Service CollectorServerService {
                  Name      = 'collector.server'
                  # Path           ='C:\Scripts\LabCollector\Server\collector.server.exe'
                  # Path           = (Join-Path @DestServerPath -ChildPath 'collector.server.exe')
                  # BuiltInAccount = 'NetworkService'
                  # StartupType    = 'Automatic'
                  # DisplayName    = 'LabCollector Server'
                  # Description    = 'LabCollector Server is a ZeroMQ (0MQ) + dotnet-core service that records collected data in a MongoDB Database.'
                  # State          = 'Running'
                  Ensure    = $Ensure
                  # Dependencies   = @('mongodb')
                  DependsOn = @(
                    '[Firewall]CollectorServiceInboundAllowTCP'
                    '[Firewall]CollectorServiceOutboundAllowTCP'
                    '[Group]AddProxyUserToAdmin'
                  )
                }

                # Setup Firewall Rules
                $RuleTypes = @(
                  foreach ($p in @('UDP', 'TCP')) {
                    foreach ($d in @('Inbound', 'Outbound')) {
                      [PSCustomObject]@{
                        Protocol  = $p
                        Direction = $d
                      }
                    }
                  }
                )

                foreach ($type in $RuleTypes) {
                  Firewall ('CollectorService{0}Allow{1}' -f $type.Direction, $type.Protocol) {
                    Name        = ('CollectorService{0}Allow{1}' -f $type.Direction, $type.Protocol)
                    Protocol    = ($type.Protocol)
                    DisplayName = ('Allow collector.server.exe {0}' -f $type.Direction)
                    Profile     = @('Domain', 'Private')
                    Program     = 'C:\Scripts\LabCollector\Server\collector.server.exe'
                    Enabled     = $true
                    Direction   = ($type.Direction)
                    Ensure      = $Ensure
                  }
                }

                Group AddProxyUserToAdmin {
                  GroupName        = 'Administrators'
                  Ensure           = 'Present'
                  MembersToExclude = $ProxyCredentials.UserName
                }
              }
            }

            $ProxyWrapperStates = @{
              OutputPath       = (Join-Path @DSCOutputRoot -ChildPath 'SetupProxyWrapper')
              ProxyCredentials = $ProxyCredentials
              Ensure           = $Ensure
              DestServerPath   = $DestServerPath
              SourceServerPath = $SourceServerPath
              Site             = $Site
            }

            Write-Host '--- Compile DSC [SetupProxyWrapper]'

            SetupProxyWrapper @ProxyWrapperStates

            $InvokeCfg = @{
              Path         = $ProxyWrapperStates['OutputPath']
              ComputerName = 'localhost'
              Wait         = $true
              Force        = $true
            }

            CheckLCM -Force:$Force

            Write-Host '--- Invoke DSC [SetupProxyWrapper]'

            Start-DscConfiguration @InvokeCfg
          }

          Default {
            Write-Host "--- SetupType [$_] not defined for [$($PSCmdlet.ParameterSetName)]"
          }
        }
      }
    }

    'WebApp' {
      # Tasks should be done in this order.
      $FixedOrder = @('Features', 'Packages', 'Modules', 'Certificate', 'Scripts')

      # Select the specified Setup tasks from the fixed order, in order.
      $SetupTasks = $FixedOrder | Where-Object { $SetupTasks -EQ $_ }
      # Import Configuration File *.cfg.psd1
      $ConfigPath = @{
        Path = (Join-Path -Path $PSScriptRoot -ChildPath "$((Get-ChildItem $MyInvocation.InvocationName).BaseName).cfg.psd1")
      }
      # Write-Verbose "MyInvocation"
      # Write-Verbose ($MyInvocation | ConvertTo-Json -Depth 1)
      Write-Verbose ($ConfigPath | ConvertTo-Json)
      $Config = Import-PowerShellDataFile @ConfigPath

      # Generate Path names
      $SubPath = @{ ChildPath = 'WebApp' }

      # Build DSC to do the rest
      $DestServerPath = @{ Path = Join-Path @LabCollectorLocalPath @SubPath }
      Write-Verbose ($DestServerPath | ConvertTo-Json)
      $WebAppPath = @{ Path = 'C:\inetpub\LabCollectorWebAppRoot\' }
      Write-Verbose ($WebAppPath | ConvertTo-Json)
      $SourceServerPath = @{ Path = Join-Path @LabCollectorSourcePath @SubPath }
      Write-Verbose ($SourceServerPath | ConvertTo-Json)

      if ($Ensure -eq 'Present') {
        switch ($SetupTasks) {
          'Features' {
            Configuration SetupWindowsFeatures {
              param(
                [string]$Ensure
              )

              Import-DScResource -ModuleName 'PSDesiredStateConfiguration'

              Node localhost {

                LocalConfigurationManager {
                  ConfigurationMode  = 'ApplyOnly'
                  RebootNodeIfNeeded = $true
                  ActionAfterReboot  = 'ContinueConfiguration'
                }

                # Enable Required Windows Features
                WindowsFeatureSet IISFeatures {
                  Name   = @(
                    'Web-Server'
                    'Web-Windows-Auth'
                    'Web-Scripting-Tools'
                    'Web-Mgmt-Console'
                    'Web-Mgmt-Service'
                  )
                  Ensure = $Ensure
                }

                Registry EnableRemoteMgmt {
                  Key       = 'HKEY_LOCAL_MACHINE\Software\Microsoft\WebManagement\Server'
                  ValueName = 'EnableRemoteManagement'
                  # dword:00000001
                  ValueData = 1
                  ValueType = 'Dword'
                  DependsOn = '[WindowsFeatureSet]IISFeatures'
                }

                Service IISRemoteMgmt {
                  Name        = 'WMSVC'
                  State       = 'Running'
                  StartupType = 'Automatic'
                  DependsOn   = '[Registry]EnableRemoteMgmt'
                }
              }
            }

            $SetupWindowsFeaturesStates = @{
              OutputPath = (Join-Path @DSCOutputRoot -ChildPath 'SetupWindowsFeatures')
              Ensure     = $Ensure

            }

            Write-Host '--- Compile DSC [SetupWindowsFeatures]'

            SetupWindowsFeatures @SetupWindowsFeaturesStates

            $LCMOptions = @{
              Path  = $SetupWindowsFeaturesStates['OutputPath']
              Force = $true
            }

            Write-Host '--- Setting LCM for [SetupWindowsFeatures]'

            Set-DscLocalConfigurationManager @LCMOptions

            $InvokeCfg = @{
              Path         = $SetupWindowsFeaturesStates['OutputPath']
              ComputerName = 'localhost'
              Wait         = $true
              Force        = $true
            }

            CheckLCM -Force:$Force

            Write-Host '--- Invoke DSC [SetupWindowsFeatures]'

            Start-DscConfiguration @InvokeCfg
          }
          'Packages' {
            Write-Host '--- [Packages] dotnet-hosting Install'
            # first. Install dotnet-hosting, dotnet-sdk; This might restart.
            & '\\naslan\MSIDeploy\DeployMSIPackage.ps1' -PackageNames 'dotnet-hosting', 'dotnet-sdk' -ForcedRestart -Verbose -Force:$Force
          }

          'Modules' {
            Write-Host '--- [Modules] Install: Please use SetupModules.ps1'
          }

          'Certificate' {
            $CertOptions = $Config['Certificate']

            # Make sure the Cert Destination exists
            Write-Host '--- [Certificate] Checking that certifcate container exists'
            $CertContainer = try {
              Test-Path -Path $CertOptions['DestinationPath'] -PathType Container
            } catch {
              # This throws an error if it doesn't exist. Le. Sigh.
              $false
            }

            if (!$CertContainer) {
              # If it doesn't exist, create it.
              Write-Host '  + [Certificate] Creating certifcate container'
              New-Item -Path $CertOptions['DestinationPath'] -ItemType Directory > $null
            } else {
              Write-Host '  = [Certificate] Certifcate container exists'
            }

            $CertificatePath = @{
              Path     = (Join-Path $CertOptions['DestinationPath'] $CertOptions['ThumbPrint'])
              PathType = 'Leaf'
            }

            Write-Host '--- [Certificate] Checking that certifcate exists'
            # Check if certificate exists
            if (!(Test-Path @CertificatePath)) {
              # Import the PFX
              $PfxOptions = @{
                Exportable        = $true
                Password          = $PfxCreds.Password
                CertStoreLocation = $CertOptions['DestinationPath']
                FilePath          = $CertOptions['Path']
                Verbose           = $true
              }

              Write-Host '  + [Certificate] Importing certifcate'
              Import-PfxCertificate @PfxOptions
            } else {
              Write-Host '  = [Certificate] Certifcate exists'
            }
          }

          'Scripts' {
            Write-Host '--- [Scripts]'

            $WildCardCert = $Config['Certificate']

            $HostNames = $Config[$ServerMode]['HostNames']

            Write-Verbose 'HostName:'
            Write-Verbose ($HostNames | ConvertTo-Json)

            Configuration SetupCollectorWebApp {
              param(
                [string]$Ensure,
                [hashtable]$CertData,
                [string[]]$HostNames,
                [hashtable]$DestServerPath,
                [hashtable]$WebAppPath,
                [hashtable]$SourceServerPath
              )

              Import-DScResource -ModuleName 'PSDesiredStateConfiguration'
              # Import-DScResource -ModuleName 'CertificateDsc'
              Import-DscResource -ModuleName 'xWebAdministration'

              Node localhost {
                # This is going to require a Set-DscLocalConfigurationManager
                LocalConfigurationManager {
                  ConfigurationMode  = 'ApplyOnly'
                  RebootNodeIfNeeded = $true
                  ActionAfterReboot  = 'ContinueConfiguration'
                }

                # Import the Wildcard Cert
                # This is having issues with the credentials
                # PfxImport WildCardCert {
                #   Ensure       = 'Present'
                #   ThumbPrint   = $CertData['ThumbPrint']
                #   Path         = $CertData['Path']
                #   Location     = $CertData['Location']
                #   Store        = $CertData['Store']
                #   Credential   = $PFXCreds
                #   FriendlyName = $CertData['FriendlyName']
                # }

                # File WebHostingCertDir {
                #   DestinationPath = $CertData['DestinationPath']
                #   Ensure          = 'Present'
                #   Type            = 'Directory'
                # }

                # Stop the default website
                xWebsite DefaultSite {
                  Ensure = 'Absent'
                  Name   = 'Default Web Site'
                  # State        = 'Stopped'
                  # PhysicalPath = 'C:\inetpub\wwwroot'
                  # DependsOn = '[WindowsFeatureSet]IISFeatures'
                }

                xWebAppPool DefaultAppPool {
                  Ensure    = 'Absent'
                  Name      = 'DefaultAppPool'
                  # State        = 'Started'
                  # autoStart    = $true
                  # identityType = 'ApplicationPoolIdentity'
                  DependsOn = '[xWebsite]DefaultSite'
                }

                File WebAppLogDir {
                  Ensure          = $Ensure
                  DestinationPath = $DestServerPath['Path']
                  Type            = 'Directory'
                }

                # Ensure the Desired webcontent is copied
                File WebAppContent {
                  DestinationPath = $WebAppPath['Path']
                  SourcePath      = (Join-Path @SourceServerPath -ChildPath 'publish')
                  Checksum        = 'SHA-256'
                  Force           = $true
                  Recurse         = $true
                  Type            = 'Directory'
                  MatchSource     = $true
                  Ensure          = $Ensure
                  DependsOn       = '[xWebsite]DefaultSite'
                }

                xWebAppPool LabCollectorAppPool {
                  Ensure       = $Ensure
                  Name         = 'LabCollectorAppPool'
                  State        = 'Started'
                  autoStart    = $true
                  identityType = 'ApplicationPoolIdentity'
                  DependsOn    = '[xWebsite]DefaultSite'
                }

                foreach ($HostName in $HostNames) {
                  Log "CheckBindings_$HostName" {
                    Message = ('Bindings [http => *:80:{0}] [https => *:443:{0}]' -f $HostName)
                  }
                  Log "CheckCertificate_$HostName" {
                    Message = ('CertData [Thumbprint => {0}] [Store => {1}]' -f $CertData['ThumbPrint'], $CertData['Store'])
                  }
                }

                $Bindings = @(
                  foreach ($HostName in $HostNames) {
                    # Http Binding
                    MSFT_xWebBindingInformation {
                      Protocol           = 'http'
                      bindingInformation = ('*:80:{0}' -f $HostName)
                    }
                    # Https Binding
                    MSFT_xWebBindingInformation {
                      Protocol              = 'https'
                      bindingInformation    = ('*:443:{0}' -f $HostName)
                      CertificateThumbprint = $CertData['ThumbPrint']
                      certificateStoreName  = $CertData['Store']
                    }
                  })

                xWebSite LabCollectorWebApp {
                  # Ensure             = $Ensure
                  Name                    = 'LabCollectorWebApp'
                  State                   = 'Started'
                  ServiceAutoStartEnabled = $true
                  ApplicationPool         = 'LabCollectorAppPool'
                  PhysicalPath            = $WebAppPath['Path']
                  # PhysicalPath       = (Join-Path @DestServerPath -ChildPath 'publish')
                  BindingInfo             = $Bindings

                  AuthenticationInfo      = MSFT_xWebAuthenticationInformation {
                    Anonymous = $false
                    Windows   = $true
                    # Basic     = $false
                    # Digest    = $false
                  }

                  DependsOn               = @(
                    '[xWebsite]DefaultSite'
                    '[File]WebAppContent'
                    '[File]WebAppLogDir'
                    '[xWebAppPool]LabCollectorAppPool'
                    # '[File]WebHostingCertDir'
                    # '[PfxImport]WildCardCert'
                  )
                }
              }
            }

            $SetupCollectorWebAppStates = @{
              OutputPath       = (Join-Path @DSCOutputRoot -ChildPath 'SetupCollectorWebApp')
              Ensure           = $Ensure
              CertData         = $WildCardCert
              HostNames        = $HostNames
              DestServerPath   = $DestServerPath
              WebAppPath       = $WebAppPath
              SourceServerPath = $SourceServerPath
            }

            Write-Host '--- Compile DSC [SetupCollectorWebApp]'

            SetupCollectorWebApp @SetupCollectorWebAppStates

            $LCMOptions = @{
              Path  = $SetupCollectorWebAppStates['OutputPath']
              Force = $true
            }

            Write-Host '--- Setting LCM for [SetupCollectorWebApp]'

            Set-DscLocalConfigurationManager @LCMOptions

            $InvokeCfg = @{
              Path         = $SetupCollectorWebAppStates['OutputPath']
              ComputerName = 'localhost'
              Wait         = $true
              Force        = $true
            }

            CheckLCM -Force:$Force

            Write-Host '--- Invoke DSC [SetupCollectorWebApp]'

            Start-DscConfiguration @InvokeCfg
          }

          Default {
            Write-Host "--- SetupType [$_] not defined for [$($PSCmdlet.ParameterSetName)]"
          }
        }
      } else {
        # Do things in reverse order when we're removing things.
        [array]::Reverse($SetupTasks)

        switch ($SetupTasks) {
          'Packages' {
            Write-Host '--- [Packages] dotnet-hosting Remove'
            # first. Install dotnet-hosting; This might restart.
            & '\\naslan\MSIDeploy\DeployMSIPackage.ps1' -PackageNames 'dotnet-hosting', 'dotnet-sdk' -ForcedRestart -Verbose -Remove -Force:$Force
          }

          'Modules' {
            Write-Host '--- [Modules] Remove: Not being removed.'
          }

          'Scripts' {
            Write-Host '--- [Scripts]'
          }

          Default {
            Write-Host "--- SetupType [$_] not defined for [$($PSCmdlet.ParameterSetName)]"
          }
        }
      }
    }

    Default {
      Write-Warning "--- SetupType [$SetupType] not defined."
    }
  }
} catch {
  throw $_
} finally {
  Write-Host "--- StopTime [$([datetime]::Now)]"
  Stop-Transcript
}
