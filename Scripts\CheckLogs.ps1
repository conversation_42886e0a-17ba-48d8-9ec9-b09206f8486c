[CmdletBinding()]
param (
  [Parameter()]
  [string]
  $ServerPath = '\\labcollectordev\c$\Scripts\LabCollector\Server',
  # Server Name
  [Parameter()]
  [string]
  $ServerName = 'labcollectordev.intra.camosun.bc.ca',
  # Parameter help description
  [Parameter()]
  [string]
  $LogFilePath,
  # TaskTypes
  [Parameter()]
  [ValidateSet('UnRegister', 'NoDeviceGroup')]
  [string[]]
  $TaskTypes,
  # Select using Out-GridView
  [Parameter()]
  [switch]
  $Select,
  # Fix it
  [Parameter()]
  [switch]
  $FixIt
)

try {

  $Green = @{
    ForegroundColor = 'Green'
  }

  $Magenta = @{
    ForegroundColor = 'Magenta'
  }

  Start-Transcript -Path "$PSScriptRoot\CheckLogs.log" -Force
  Push-Location -Path $ServerPath -Verbose
  Write-Host @Magenta "--- PWD [$PWD]"

  if (![string]::IsNullOrEmpty($LogFilePath)) {
    Write-Host @Magenta "--- Checking [$LogFilePath]"

    $LogExists = Test-Path -Path $LogFilePath -PathType Leaf

    if ($LogExists) {
      $LogItem = Get-Item -Path $LogFilePath
      Write-Host @Magenta "--- Found [$($LogItem.FullName)]"
    }
  } else {
    $LogExists = $false
  }

  $rgPath = (where.exe rg.exe)

  if (![string]::IsNullOrEmpty($rgPath)) {
    $RipGrepFound = Test-Path -Path $rgPath -PathType Leaf
    if ($RipGrepFound) {
      Write-Host "--- RipGrep (rg) found [$rgPath]"
    } else {
      Write-Host "--- RipGrep on path, but not found? [$rgPath]"
      throw 'RipGrepPathError'
    }
  } else {
    Write-Host '--- RipGrep not found'
    throw 'RipGrepNotFoundError'
  }

  foreach ($Task in $TaskTypes) {
    switch ($Task) {
      'NoDeviceGroup' {
        $Title = $LogExists ? ($Task,$LogItem.Name) -join ' : ' : $Task
        Write-Host @Green "--- [$Title] Processing..."

        $SearchPattern = '^.*Returning empty guid \[(.*):00000000-0000-0000-0000-000000000000:(.*)\]'
        $ReplacePattern = '{\"HostName\":\"$1\",\"SerialNumber\":\"$2\"}'

        $Results = if ($LogExists) {
          rg -uu $SearchPattern -r $ReplacePattern -N -I $LogItem.FullName |
            Select-Object -Unique | Sort-Object | ConvertFrom-Json
        } else {
          rg -uu $SearchPattern -r $ReplacePattern -N -I |
            Select-Object -Unique | Sort-Object | ConvertFrom-Json
        }

        if ($Results.Count -eq 0) {
          Write-Host @Green "--- [$Title] No Devices found"
        } else {
          Write-Host @Green "--- [$Title] Devices found [$($Results.Count)]"

          if ($Select) {
            $Results = $Results | Out-GridView -PassThru -Title $Title
          }

          if ($FixIt) {
            Write-Host @Green '--- Please add the following hosts to their respective device groups in AD.'
          }

          $Results | Format-Table -AutoSize | Out-String
        }
        Write-Host @Green "--- [$Title] Done."
      }
      'UnRegister' {
        $Title = $LogExists ? ($Task, $LogItem.Name) -join ' : ' : $Task

        Write-Host @Green "--- [$Title] Processing..."

        $SearchPattern = '^.*\[ProcessingRegistration\] Please unregister <(.*)>'
        $ReplacePattern = '$1'

        $Results = if ($LogExists) {
          rg -uu $SearchPattern -r $ReplacePattern -N -I $LogItem.FullName |
            Select-Object -Unique | Sort-Object -Property HostName | ConvertFrom-Json
        } else {
          rg -uu $SearchPattern -r $ReplacePattern -N -I |
            Select-Object -Unique | Sort-Object -Property HostName | ConvertFrom-Json
        }

        if ($Results.Count -eq 0) {
          Write-Host @Green "--- [$Title] No Devices found"
        } else {
          Write-Host @Green "--- [$Title] Devices found [$($Results.Count)]"

          if ($Select) {
            $Results = $Results | Out-GridView -PassThru -Title $Title
          }

          $OutDir = @{ Path = "$PSScriptRoot\UnRegister" }

          # Create the unregister directory
          if (!(Test-Path @OutDir -PathType Container)) {
            New-Item @OutDir -ItemType Directory -Force -Verbose
          }

          $CompareOptions = @{
            Property = @(
              'Type_GUID'
              'HostName'
              'SerialNumber'
              'IsRegistering'
            )
          }

          foreach ($item in $Results) {
            $itemJSON = $item | ConvertTo-Json -Compress

            $registerPath = @{ Path = Join-Path @OutDir -ChildPath "$($item.HostName).registration.json" }
            $clientPath = @{ Path = Join-Path @OutDir -ChildPath "$($item.HostName).client.log" }

            $registerExists = Test-Path @registerPath -PathType Leaf

            if ($registerExists) {
              $registerContent = Get-Content @registerPath
              $registerObject = $registerContent | ConvertFrom-Json
              $registerMatches = if (Compare-Object $registerObject $item @CompareOptions) {
                $false
              } else {
                $true
              }
            } else {
              $registerContent = $null
              $registerMatches = $false
            }

            $clientExists = Test-Path @clientPath -PathType Leaf

            # When unregister happens, it writes out a client.json file.
            # We need to make sure that
            if (
              $clientExists -and
              $registerExists -and
              $registerMatches
            ) {
              Write-Host "--- Client already unregistered [$($item.HostName); Matches: $registerMatches]"
              Write-Host "  ? [Search < $itemJSON >]"
              Write-Host "  = [Exists < $registerContent >]"

              # Registration, Client data exist
              continue
            }

            if (
              !$clientExists -and
              $registerExists
            ) {
              Write-Host "--- Waiting to be unregistered [$($item.HostName); Matches: $registerMatches]"
              Write-Host "  ? [Search < $itemJSON >]"
              Write-Host "  = [Exists < $registerContent >]"
              # if (!$FixIt) {
              #   continue
              # }
            } else {
              Write-Host "--- New registration [$($item.HostName); Matches: $registerMatches]"
              Write-Host "  ? [Search < $itemJSON >]"
            }

            $OutFileOptions = @{
              FilePath = $registerPath['Path']
              Encoding = 'utf8'
              Verbose  = $true
              Force    = $true
            }

            if (!$registerMatches) {
              $itemJSON | Out-File @OutFileOptions
            }

            $Command = @"
.\collector.client.exe register -s="$ServerName" -d@="$($registerPath['Path'])" -o="$($clientPath['Path'])"
"@

            if ($FixIt) {
              $TeeOptions = @{
                FilePath = $clientPath['Path']
                Encoding = 'utf8'
                # Verbose  = $true
                # Force    = $true
              }
              Invoke-Expression -Command $Command | Tee-Object @TeeOptions
            } else {
              Write-Host @Green "--- Command To Run < $Command >"
            }
          }
        }
        Write-Host @Green "--- [$Title] Done."
      }
      Default {
        Write-Host @Green "--- [$_] TaskType Not defined"
      }
    }
  }
} catch {
  throw $_
} finally {
  Pop-Location -Verbose
  Stop-Transcript
}