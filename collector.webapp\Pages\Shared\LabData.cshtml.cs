using collector.common.model;
using collector.common.service;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.StaticFiles;

namespace collector.webapp.Pages;

[Authorize]
public class LabDataModel(ILogger<LabDataModel> logger) : PageModel
{
  private readonly ILogger<LabDataModel> _logger = logger;
  private readonly List<string> ValidPages =
  [
    "/Student",
    "/Staff",
    "/Admin"
  ];

  [BindProperty(SupportsGet = true)]
  public string RoomName { get; set; }

  public Lab lab;

  public List<Client> clients;

  public async Task<IActionResult> OnGetAsync()
  {
    // Check the route Data
    var routeDataJson = System.Text.Json.JsonSerializer.Serialize(RouteData);
    _logger.LogInformation("RouteData : [{routeDataJson}]", routeDataJson);
    // Get the page value
    var page = RouteData.Values["page"] as string;
    // Check page to see if it's valid
    if (null != page && !ValidPages.Contains(page)) return NotFound();

    // Check the RoomName to see if it's valid.
    bool IsValidRoomName = await CheckRoomName();
    _logger.LogInformation("RoomName:[{RoomName}:{IsValidRoomName}]", RoomName, IsValidRoomName);
    if (!IsValidRoomName) return NotFound();

    // Get the lab from the DB
    lab = await MongoDBService.LabRead(RoomName);
    if (null == lab) return NotFound();

    // Check the lab is enabled and the permissions against the page
    bool IsPermitted = lab.Enabled == true && page switch
    {
      "/Student" => lab.IsStaffOnly == false && lab.IsAdminOnly == false,
      "/Staff" => lab.IsAdminOnly == false,
      "/Admin" => true,
      _ => false
    };

    _logger.LogInformation("Found Lab [{RoomName}:{Lab_GUID}] Permission [{page}:{IsPermitted}]", lab.RoomName, lab.Lab_GUID, page, IsPermitted);
    if (!IsPermitted) return NotFound();

    // This doesn't work when there are no clients.
    clients = await MongoDBService.ClientRead(lab);
    _logger.LogInformation("Found [{Count}] clients", clients.Count);
    if (clients.Count != 0)
      lab.LastUpdated = clients.Max(c => c.LastUpdated);

    return Page();
  }

  public async Task<bool> CheckRoomName()
  {
    // Check the RoomName is not null
    if (null == RoomName) return false;

    // Get the list of Names
    var ValidRoomNames = await MongoDBService.LabReadAllRoomNames();

    // Check the RoomName against the list.
    bool IsValidName = ValidRoomNames.Contains(RoomName);
    return IsValidName;
  }

  public async Task<FileStreamResult> OnPostRDPAsync(string DNSHostName)
  {
    //https://www.learnrazorpages.com/razor-pages/handler-methods#parameters-in-handler-methods
    //https://darchuk.net/2019/05/31/asp-net-core-web-api-returning-a-filestream/
    _logger.LogInformation(" --- [OnPostRDPAsync] Generating RDP for {DNSHostName}", DNSHostName);
    // Generate the rdpString for the client
    var rdpStrings = new List<string>(){
          $"full address:s:{DNSHostName}",
          "audiocapturemode:i:1",
          "audiomode:i:0",
          "dynamicresolution:i:1"
        };
    // Create a byte[] response from the strings
    var rdpResponse = System.Text.Encoding.UTF8.GetBytes(
        string.Join(Environment.NewLine, rdpStrings)
      );
    // Define an output file name.
    var fileName = $"{DNSHostName}.rdp";

    //Attempt to generate a contentType
    var provider = new FileExtensionContentTypeProvider();
    if (!provider.TryGetContentType(fileName, out string? contentType))
      //default type
      contentType = "application/rdp";

    // Create a MemoryStream Object for the StreamWriter
    var ms = new MemoryStream();

    // Write the rdpResponse to the stream
    await ms.WriteAsync(rdpResponse);

    // Rewind the MemoryStream for handoff
    ms.Seek(0, SeekOrigin.Begin);

    _logger.LogInformation(" --- [OnPostRDPAsync] Generated [{fileName}:{contentType}]", fileName, contentType);

    // Return the File response
    return File(ms, contentType, fileName);
    // ASP.NET Core disposes of the ms object when it's finished.
  }
}
