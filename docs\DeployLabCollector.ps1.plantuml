@startuml DeployLabCollector.ps1
start
partition #LightBlue "Initialization" {
  :Initialize transcript directory and log file.
  Initialize additional variables.
  ]
}

partition #LightCoral "Client DSC" {
  fork
  : Ensure == 'Present' ]
  : File ClientDirectory >
  while (ForEach <File> in <DSCSources\LabCollector\Client\*>)
  : Script ClientFiles_<File> >
  endwhile
  : Script RegisterCollectorClient >
  : Script SetupCollectorClient >
  fork again
  : Ensure != 'Present' ]
  : Script SetupCollectorClient >
  : Script RegisterCollectorClient >
  : File ClientFiles >
  end fork
}

partition #LightSeaGreen "Apply DSC" {
  :Compile DSC;
  :Check Local Configuration Manager (LCM);
  :Start DSC Configuration;
}

:Stop Transcript>
stop
@enduml
