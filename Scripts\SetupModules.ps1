$OutputPath = @{ Path = 'C:\Scripts\' }

Write-Host '--- [Modules] Install'

# 2. DSC to install required Modules
Configuration InstallPowerShellModules {
  param(
    [string[]]$PSModules,
    [string]$Ensure
  )

  Import-DscResource -ModuleName 'PSDesiredStateConfiguration'
  Import-DscResource -ModuleName 'PackageManagement' -ModuleVersion 1.4.7

  Node localhost {
    File ScriptsDirectoryExists {
      DestinationPath = 'C:\Scripts\'
      Type            = 'Directory'
      Ensure          = 'Present'
    }

    PackageManagementSource PSGallery {
      Name               = 'PSGallery'
      Ensure             = 'Present'
      InstallationPolicy = 'Trusted'
      ProviderName       = 'PowerShellGet'
      SourceLocation     = 'https://www.powershellgallery.com/api/v2/'
    }

    foreach ($Module in $PSModules) {
      PackageManagement $Module {
        Ensure    = $Ensure
        Name      = $Module
        Source    = 'PSGallery'
        DependsOn = '[PackageManagementSource]PSGallery'
      }
    }
  }
}

$ModuleStates = @{
  OutputPath = (Join-Path @OutputPath -ChildPath 'InstallPowerShellModules')
  PSModules  = @(
    'psake'
    'MDBC'
    'IISAdministration'
    # 'CertificateDsc'
    'xWebAdministration'
    'NetworkingDsc'
    'Microsoft.PowerShell.SecretManagement'
    'Microsoft.PowerShell.SecretStore'
  )
  Ensure     = 'Present'
}

Write-Host '--- Compile DSC [InstallPowerShellModules]'

InstallPowerShellModules @ModuleStates

$InvokeCfg = @{
  Path         = $ModuleStates['OutputPath']
  ComputerName = 'localhost'
  Wait         = $true
  Force        = $true
  Verbose      = $true
}

Write-Host '--- Invoke DSC [InstallPowerShellModules]'

Start-DscConfiguration @InvokeCfg
