@{
  # Webserver root share\directory
  ServerRoot = '\\softtsrv\Sites'
  Labs       = @(
    <# Lansdowne, Ewing Building  EWG100 #>
    @{
      GUID           = 'b963544e-5a81-4f9c-9a32-612637c343e7'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG100')
      IsGroup        = $false
      SamAccountName = 'EWG100_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG102 #>
    @{
      GUID           = 'e380f3eb-7f20-4b86-92b3-8b1b2412b682'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG102')
      IsGroup        = $false
      SamAccountName = 'EWG102_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG110 #>
    @{
      GUID           = 'bd818474-c5dc-430f-bf2a-0e7e62e3b9ff'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG110')
      IsGroup        = $false
      SamAccountName = 'EWG110_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG112 #>
    @{
      GUID           = '6248a236-bb6f-4dbd-8e29-9b83915ba9b9'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG112')
      IsGroup        = $false
      SamAccountName = 'EWG112_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG115 #>
    @{
      GUID           = 'a040184a-06f8-41c8-b2db-c7101df4a401'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG115')
      IsGroup        = $false
      SamAccountName = 'EWG115_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG200 #>
    @{
      GUID           = 'be9bc246-ff5e-4777-8af1-e9fd409bf6fb'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG200')
      IsGroup        = $false
      SamAccountName = 'EWG200_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG202 #>
    @{
      GUID           = '3a3be383-a519-41aa-a383-4d919406f104'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG202')
      IsGroup        = $false
      SamAccountName = 'EWG202_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Ewing Building  EWG342 #>
    @{
      GUID           = '11050e8b-47e5-43bd-abfd-239502227d67'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG342')
      IsGroup        = $false
      SamAccountName = 'EWG342_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Fisher Building  FSH228 #>
    @{
      GUID           = 'ad9edd45-7391-409e-9570-8010cbd63833'
      Department     = 'Lansdowne, Fisher Building '
      LabNames       = @('FSH228')
      IsGroup        = $false
      SamAccountName = 'FSH228_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Fisher Building FSH264 #>
    @{
      GUID           = 'c3ca5c37-f1d2-44ae-9313-a47a1ec923d0'
      Department     = 'Lansdowne, Fisher Building'
      LabNames       = @('FSH264')
      IsGroup        = $false
      SamAccountName = 'FSH264_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Fisher Building  FSH318 #>
    @{
      GUID           = '3b84aeb7-2fde-4e5a-b02d-ef41b72922ad'
      Department     = 'Lansdowne, Fisher Building '
      LabNames       = @('FSH318')
      IsGroup        = $false
      SamAccountName = 'FSH318_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Fisher Building  FSH358 #>
    @{
      GUID           = '9f4e876b-b349-4233-bc8d-ba6257efdc6a'
      Department     = 'Lansdowne, Fisher Building '
      LabNames       = @('FSH358')
      IsGroup        = $false
      SamAccountName = 'FSH358_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Library & Learning Commons LLC114 # >
    @{
      GUID           = 'd5bb8d59-f317-4670-a44f-97bee84bcaec'
      Department     = 'Lansdowne, Library & Learning Commons'
      LabNames       = @('LLC114')
      IsGroup        = $false
      SamAccountName = 'LLC114_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Library & Learning Commons LLC114MM # >
    @{
      GUID           = 'e390ce1c-6c35-4ec7-96cc-3b34e57198c2'
      Department     = 'Lansdowne, Library & Learning Commons'
      LabNames       = @('LLC114MM')
      IsGroup        = $false
      SamAccountName = 'LLC114MM_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Library & Learning Commons LLC136 #>
    @{
      GUID           = '90d7eb75-77ce-47ca-a6b2-5a8158b3d928'
      Department     = 'Lansdowne, Library & Learning Commons'
      LabNames       = @('LLC136')
      IsGroup        = $false
      SamAccountName = 'LLC136_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Library & Learning Commons LLC209 #>
    @{
      GUID           = 'a9a17bc2-4f8c-44e3-b8f9-db42805b7497'
      Department     = 'Lansdowne, Library & Learning Commons'
      LabNames       = @('LLC209')
      IsGroup        = $false
      SamAccountName = 'LLC209_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Young Building Y109 #>
    @{
      GUID           = '7987f965-624d-4d45-9729-952d6898d7db'
      Department     = 'Lansdowne, Young Building'
      LabNames       = @('Y109')
      IsGroup        = $false
      SamAccountName = 'Y109_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Young Building Y307 #>
    @{
      GUID           = '2b3a10f8-d2b4-424a-86be-939459ad6d87'
      Department     = 'Lansdowne, Young Building'
      LabNames       = @('Y307')
      IsGroup        = $false
      SamAccountName = 'Y307_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Young Building Y311 #>
    @{
      GUID           = 'b6a4a1a5-2c4a-4526-8917-633dd4bd27cc'
      Department     = 'Lansdowne, Young Building'
      LabNames       = @('Y311')
      IsGroup        = $false
      SamAccountName = 'Y311_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Lansdowne, Young Building YNG107 #>
    @{
      GUID           = '5c181b12-1368-4ee2-adfc-9f59f67d36c9'
      Department     = 'Lansdowne, Young Building'
      LabNames       = @('YNG107')
      IsGroup        = $false
      SamAccountName = 'YNG107_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
  )
}
