@{
  # Webserver root share\directory
  ServerRoot = '\\softtsrv\Sites'
  Labs       = @(
    <# Example Template # >
    @{
      GUID           = 'af1a0895-6271-422f-ac6d-640bf6289b22' # Generate with 'New-Guid'
      # Lab area, used to label HTML Table Label
      Department     = 'Electronics Labs'
      # If IsGroup is true, group these together as part of the label
      # and show as one table => '$Department : '+$LabNames -join ', '
      # Otherwise generate separate pages : $Department : $LabNames.Element
      LabNames       = @('CBA119', 'CBA124', 'TB204', 'TB205', 'TB227', 'TB229')
      IsGroup        = $true
      # Required if IsGroup is true, must be the name of an AD Security group that has the desired lab machines in it.
      SamAccountName = ''
      # Required if IsGroup is true, must use this as the OutputFile
      OutputFile     = '\\softtsrv\Sites\Elex.camosun.bc.ca\wwwroot\Student\Check_RDPLogin.html'
      # If IsGroup is false, SiteApp and <PERSON>Root used to define the OutputFile
      # Note that this should create a directory in the form:
      # \\$ServerRoot\$SiteApp\$SiteRoot\$LabNames.Element\[index.html],[<MachineNames>.rdp]
      SiteApp        = 'Elex.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>

    <# Current Production #>

    <# Instructor Stations Test # >
    @{
      GUID           = 'ba43501a-a55e-4f0a-9d90-9865592ba063'
      Department     = 'Interurban, LACC' # Descriptive Name for Lab/Department
      LabNames       = @('ILC340') # The name of the lab for Single page or Sub page
      IsGroup        = $true # True: Single page for all labs; False: one page per Lab Name
      SamAccountName = 'ILC340_devices' # Or SamAccountName for lab machine Security group
      OutputFile     = '\\softtsrv\Sites\MyLabAccess.camosun.bc.ca\wwwroot\TestGroup\index.html' # Dedicated output file, if IsGroup = $true. Otherwise generated dynamically.
      SiteApp        = 'MyLabAccess.camosun.bc.ca' # Site application on WebServer, used if IsGroup = $false
      SiteRoot       = 'wwwroot\TestGroup' # Default site root ==> \\$ServerRoot\$SiteApp\$SiteRoot\$LabNames\
    }#>
    <# Instructor Stations Prod # >
    @{
      GUID           = '28b63c6c-c048-4e6d-8a5e-6eff0d013014'
      Department     = 'Instructor Workstations' # Descriptive Name for Lab/Department
      LabNames       = @('Staff') # The name of the lab for Single page or Sub page
      IsGroup        = $true # True: Single page for all labs; False: one page per Lab Name
      SamAccountName = 'InstructorLabWorkstations' # Or SamAccountName for lab machine Security group
      OutputFile     = '\\softtsrv\Sites\MyLabAccess.camosun.bc.ca\wwwroot\Staff\index.html' # Dedicated output file, if IsGroup = $true. Otherwise generated dynamically.
      SiteApp        = 'MyLabAccess.camosun.bc.ca' # Site application on WebServer, used if IsGroup = $false
      SiteRoot       = 'wwwroot\Staff' # Default site root ==> \\$ServerRoot\$SiteApp\$SiteRoot\$LabNames\
    }#>
    <# Interurban, LACC ILC340 # >
    @{
      GUID           = '39bbc4ea-c549-46c1-85c5-09aadcd51dbc'
      Department     = 'Interurban, LACC'
      LabNames       = @('ILC340')
      IsGroup        = $false
      SamAccountName = 'ILC340_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Ewing CE # >
    @{
      GUID           = 'e380f3eb-7f20-4b86-92b3-8b1b2412b682'
      Department     = 'Lansdowne, Ewing Building (CE)'
      LabNames       = @('EWG102')
      IsGroup        = $false
      SamAccountName = ''
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# TestStations #>
    @{
      GUID           = '792a33fe-3f20-4660-aebe-34caa91a2fe7'
      Department     = 'Test Stations'
      LabNames       = @('TestStations')
      IsGroup        = $true
      SamAccountName = 'TestStations'
      OutputFile     = '\\softtsrv\Sites\MyLabAccess.camosun.bc.ca\wwwroot\TestStations\index.html'
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\TestStations'
      UseMongoDB     = $true
    }#>
  )
}
