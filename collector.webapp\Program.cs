using collector.common;
using collector.webapp.Handlers;

using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Authorization;

using Serilog;
using Serilog.Sinks.File.Archive;

//  Specifiy Serilog Configuration
Log.Logger = new LoggerConfiguration()
  .MinimumLevel.Information()
  .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Information)
  .Enrich.FromLogContext()
  .WriteTo.Console()
  .WriteTo.Async(f => f.File(
    path: @"C:/Scripts/LabCollector/WebApp/Logs/collector.webapp.log",
    rollingInterval: RollingInterval.Day,
    retainedFileCountLimit: 7,
    hooks: new ArchiveHooks(System.IO.Compression.CompressionLevel.Optimal)
  ))
  .CreateLogger();

// Configure a builder object
var builder = WebApplication.CreateBuilder(args);

// Add Serilog Handling.
builder.Host.UseSerilog();

// Add services to the container.
// builder.Services.AddSingleton<IClaimsTransformation, AdClaimsTransformer>();

builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
  .AddNegotiate();

checked
{
  var calendarServerOption = builder.Configuration.GetSection("LabCalendar")
    .Get<LabCalendarOptions>();

  if (calendarServerOption is not null)
    Collector.CalendarServer = calendarServerOption.LabCalendar;
}

// SecuritySettings Configuration
checked
{
  var secOptions = builder.Configuration.GetSection("SecuritySettings")
    .Get<SecuritySettingsOptions>();

  if (secOptions is not null)
    builder.Services.AddAuthorization(
      options =>
      {
        options.FallbackPolicy = options.DefaultPolicy;
        options.AddPolicy(
          "StudentAccessPolicy",
          policy => policy.RequireAuthenticatedUser()
            .AddRequirements(new MemberOfRequirement(secOptions.StudentGroups))
        );
        options.AddPolicy(
          "StaffAccessPolicy",
          policy => policy.RequireAuthenticatedUser()
            .AddRequirements(new MemberOfRequirement(secOptions.StaffGroups))
        );
        options.AddPolicy(
          "AdminAccessPolicy",
          policy => policy.RequireAuthenticatedUser()
            .AddRequirements(new MemberOfRequirement(secOptions.AdminGroups))
        );
      }
    );
}

builder.Services.AddRazorPages(
  options =>
  {
    options.Conventions.AuthorizePage("/Student", "StudentAccessPolicy");
    options.Conventions.AuthorizePage("/Staff", "StaffAccessPolicy");
    options.Conventions.AuthorizePage("/Admin", "AdminAccessPolicy");
  }
);

// [System.Runtime.Versioning.SupportedOSPlatform("windows")]
builder.Services.AddSingleton<IAuthorizationHandler, MemberOfHandler>();

// Buid and Configure the app object
var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
  // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
  app.UseHsts();
else
  app.UseDeveloperExceptionPage();

app.UseExceptionHandler("/Error");
app.UseStatusCodePagesWithReExecute("/Errors/{0}");
app.UseSerilogRequestLogging();
app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.MapRazorPages();

try
{
  Log.Information($"=== Starting LabCollector WebApp [{collector.common.Collector.Version}] ===");
  app.Run();
}
catch (Exception ex)
{
  Log.Fatal(ex, " === Server error ===");
}
finally
{
  Log.CloseAndFlush();
}
