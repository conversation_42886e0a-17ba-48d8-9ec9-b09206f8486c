# LabCollector

Project to consolidate the various components from Collector and CheckRDP

[[_TOC_]]

## Description

__`LabCollector`__ is a system of clients (`collector.client` + PowerShell scripts), listener server (`collector.server` + PowerShell scripts), webapp (`collector.webapp`), and database (__MongoDB__). The clients collect `quser` information stores it in a MongoDB database via the listener server. The webapp provides a web interface via IIS that authenticates and authorizes users to view that data as organized by lab groups.

The component files are currently published to a central location via a `psakefile`:
`\\naslan\dscsources\LabCollector`

## Client Components

The client consists the following files:

- `collector.client.exe`
- `CollectorWrapper.ps1`
- `CollectorWrapper.cfg.psd1`

It is currently deployed via a GPO:

- `STUDENT_DeployLabCollector` (PROD)
- `TEMPLATE_STUDENT_DeployLabCollector` (DEV)
- Which both use `DeployLabCollector.ps1`

These GPOs set some client firewall rules and setup an 'immediate' scheduled task that invokes a PowerShell script (`DeployLabCollector.ps1`) from the central publish location. The client connects to port 5555 on the listening server via ZeroMQ.

See the Collector.Client [documentation here](docs/Collector.Client.md).

## Server Components

## WebApp Components

## WebApp Interface Sample

![Image](SamplePage.png)

>__Status is as follows__:
>
>- `Free` means that the computer is on and available to be used via RDP
>- `In use` means that the computer is on and someone currently has an Active session.
>- `Unavailable` means that the computer is one of:
>   - The computer is offline
>   - The computer failed to respond within a reasonable amount of time to a scan (_Default is 20s_)
>   - The computer just failed to scan for some other reason

## Other Notes

Notes to go here.
