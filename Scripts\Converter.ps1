[CmdletBinding()]
param (
  [Parameter()]
  [string]
  $InputCSV,
  [string]$ServerRoot = '\\softtsrv\Sites'
)

function HashToPSD {
  [CmdletBinding()]
  param (
    [string]$Description,
    [string]$Enabled,
    [string]$GUID,
    [string]$Department,
    [string]$LabNames,
    [ValidateSet('true', 'false')]
    [string]$IsGroup = 'false',
    [string]$SamAccountName,
    [string]$OutputFile,
    [string]$SiteApp = 'MyLabAccess.camosun.bc.ca',
    [string]$SiteRoot = 'wwwroot\Student'
  )
  $Space = if ($Enabled -eq 'TRUE') { '' } else { ' ' }
  $PSD = @"
    <# $Department $LabNames #$Space>
    @{
      GUID           = '$GUID'
      Department     = '$Department'
      LabNames       = @('$LabNames')
      IsGroup        = `$$IsGroup
      SamAccountName = '$SamAccountName'
      OutputFile     = ''
      SiteApp        = '$SiteApp'
      SiteRoot       = '$SiteRoot'
    }#>
"@
  return $PSD
}
$Header = @"
@{
  # Webserver root share\directory
  ServerRoot = '$ServerRoot'
  Labs       = @(
"@

$Footer = @'
  )
}
'@

$CSVData = Get-Content $InputCSV | ConvertFrom-Csv | Sort-Object -Property 'DeviceGroup'
$Campuses = $CSVData.Campus | Select-Object -Unique

foreach ($C in $Campuses) {
  $CampusData = $CSVData.Where({ $_.Campus -eq $C })
  $OutData = @(
    foreach ($item in $CampusData) {
      $Hash = @{
        Enabled        = ($item.Enabled)
        GUID           = ('{0}' -f $item.Lab_GUID)
        Department     = ('{0}, {1}' -f $item.Campus, $item.Building)
        LabNames       = ('{0}' -f $item.RoomName)
        SamAccountName = ('{0}' -f $item.DeviceGroup)
        # IsGroup        = (if (
        #     $item.IsStaffOnly -eq 'TRUE' -or
        #     $item.IsAdminOnly -eq 'TRUE'
        #   ) { 'true' } else { 'false' })
      }
      HashToPSD @Hash
    })
  $PSD1_String = @( $Header, $OutData, $Footer)
  $PSD1_String | Out-File -FilePath "$C.psd1" -Encoding utf8 -Force
}