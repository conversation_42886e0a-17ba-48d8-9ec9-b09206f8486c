using module ./Scripts/PackageManifest.psm1
#Requires -PSEdition Core
<#
  Deploy With:
  PS> Invoke-psake -taskList Publish, Deploy -properties @{ProdTargetDir = @{ Path = '\\naslan\DSCSources' }}
#>

#region GeneratePath
function GeneratePath {
  param([hashtable]$Source)
  @(
    foreach ($Key in $Source.Keys) {
      foreach ($child in $Source[$Key]) {
        $Splat = @{
          Path                = $Key
          ChildPath           = $child['ChildPath']
          AdditionalChildPath = $child['AdditionalChildPath']
        }
        Join-Path @Splat
      }
    }
  )
}
#endregion GeneratePath

#region PSake Properties
Properties {
  $PackageVersion = [version]'2.3.7'
  $ProjectPath = (Get-Item -Path .)
  $ProjectName = '{0}' -f $ProjectPath.BaseName
  $ScriptsDir = @{ Path = (Join-Path -Path $ProjectPath.FullName -ChildPath 'Scripts') }
  $ProjectDir = @{ Path = $ProjectPath.FullName }

  # $ProdTargetDir = @{ Path = '\\naslan\DSCSources' }
  $ProdTargetDir = @{ Path = 'C:\Temp\dotnet' }

  $FileTargets = @(
    @{
      Name       = 'CollectorClient'
      BaseName   = 'Client'
      ProjectDir = 'collector.client'
      Files      = @{
        Path        = @{
          $ScriptsDir['Path'] = @(
            @{ ChildPath = 'CollectorWrapper.ps1' }
            @{ ChildPath = 'CollectorWrapper.cfg.psd1' }
          )
          $ProjectDir['Path'] = @(
            @{
              ChildPath           = 'collector.client'
              AdditionalChildPath = @(
                'publish'
                'collector.client.exe'
              )
            }
          )
        }
        Destination = @{
          $ProdTargetDir['Path'] = @(
            @{
              ChildPath           = $ProjectName
              AdditionalChildPath = 'Client'
            }
          )
        }
      }
    }
    @{
      Name       = 'CollectorServer'
      BaseName   = 'Server'
      ProjectDir = 'collector.server'
      Files      = @{
        Path        = @{
          $ScriptsDir['Path'] = @(
            @{ ChildPath = 'Check_RDPLogin.ps1' }
            @{ ChildPath = 'ProxyWrapper.ps1' }
            @{ ChildPath = 'CollectorWrapper.cfg.psd1' }
            @{ ChildPath = 'collector.common.ps1' }
            @{ ChildPath = 'Interurban.psd1' }
            @{ ChildPath = 'Lansdowne.psd1' }
            @{ ChildPath = 'LabData.csv' }
            @{ ChildPath = 'None.psd1' }
            @{ ChildPath = 'index_footer.txt' }
            @{ ChildPath = 'index_header.txt' }
          )
          $ProjectDir['Path'] = @(
            @{
              ChildPath           = 'collector.client'
              AdditionalChildPath = @(
                'publish'
                'collector.client.exe'
              )
            }
            @{
              ChildPath           = 'collector.server'
              AdditionalChildPath = @(
                'publish'
                'collector.server.exe'
              )
            }
            @{
              ChildPath           = 'collector.server'
              AdditionalChildPath = @(
                'publish'
                'appsettings.json'
              )
            }
          )
        }
        Destination = @{
          $ProdTargetDir['Path'] = @(
            @{
              ChildPath           = $ProjectName
              AdditionalChildPath = 'Server'
            }
          )
        }
      }
    }
    @{
      Name     = 'GPODeployScript'
      BaseName = 'Setup'
      Files    = @{
        Path        = @{
          $ScriptsDir['Path'] = @(
            @{ ChildPath = 'DeployLabCollector.ps1' }
            @{ ChildPath = 'DeployServer.ps1' }
            @{ ChildPath = 'DeployServer.cfg.psd1' }
            @{ ChildPath = 'SetupModules.ps1' }
            @{ ChildPath = 'SetupSecrets.ps1' }
            @{ ChildPath = 'PackageManifest.psm1' }
          )
        }
        Destination = @{
          $ProdTargetDir['Path'] = @(
            @{
              ChildPath           = $ProjectName
              AdditionalChildPath = 'Setup'
            }
          )
        }
      }
    }
    @{
      Name       = 'WebAppPublish'
      BaseName   = 'WebApp'
      ProjectDir = 'collector.webapp'
      Folders    = @{
        Path        = @{
          $ProjectDir['Path'] = @(
            @{
              ChildPath           = 'collector.webapp'
              AdditionalChildPath = @(
                'bin'
                'Release'
                'net8.0'
                'win-x64'
                'publish'
              )
            }
          )
        }
        Destination = @{
          $ProdTargetDir['Path'] = @(
            @{
              ChildPath           = $ProjectName
              AdditionalChildPath = @(
                'WebApp'
                'publish'
              )
            }
          )
        }
      }
    }
  )
}
#endregion PSake Properties

#region PSake Default
Task Default -Depends Hello
#endregion PSake Default

#region PSake EchoParams
Task EchoParams {
  Write-Host "--- ProjectPath: [$($ProjectPath.FullName | ConvertTo-Json -Compress)]"
  Write-Host "--- ProjectName: [$($ProjectName | ConvertTo-Json -Compress)]"
  Write-Host "--- ScriptsDir: [$($ScriptsDir | ConvertTo-Json -Compress)]"
  Write-Host "--- ProjectDir: [$($ProjectDir | ConvertTo-Json -Compress)]"
  Write-Host "--- ProdTargetDir: [$($ProdTargetDir | ConvertTo-Json -Compress)]"
}
#endregion PSake EchoParams

#region PSake Hello
Task Hello {
  "-- Hello [$ProjectName]"
}
#endregion PSake Hello

#region PSake Clean
Task Clean {
  # Exec { dotnet clean }
  # Exec { dotnet clean --configuration Release }
  # Get-ChildItem -Path (Get-ChildItem -name 'collector.*' -Directory) -Directory -Recurse -Filter 'publish' |
  #   Remove-Item -Force -Recurse -Verbose
  $Paths = @(
    'collector.*/publish/*'
    'collector.*/bin/Debug/*'
    'collector.*/bin/Release/*'
    'collector.*/obj/Debug/*'
    'collector.*/obj/Release/*'
  )

  $Paths | ForEach-Object {
    Get-ChildItem -Path $_ -Directory |
      Remove-Item -Force -Recurse -Verbose
    }
  }
  #endregion PSake Clean

  #region PSake Publish
  Task Publish -Depends GitPull, Clean {
    Exec { dotnet publish --configuration Release --os win }
  }
  #endregion PSake Publish

  #region PSake Deploy
  Task Deploy -Depends GitPull {
    Write-Host ('=== Publish to [{1} in {0}]' -f $ProdTargetDir['Path'], $ProjectName)

    foreach ($FileSet in $FileTargets) {
      Write-Host ('--- Syncing [{0}]' -f $FileSet['Name'])

      # This consolidates files from various locations in the source into a single
      # directory.
      if ($FileSet.ContainsKey('Files')) {
        # Foreach fileset, generate full joined paths for all Path items and Destinations
        $Files = [PSCustomObject]@{
          Path        = (GeneratePath $FileSet.Files['Path'])
          Destination = (GeneratePath $FileSet.Files['Destination'])
        }

        # Need to ensure the Destination Path exists
        if (!(Test-Path -Path $Files.Destination)) {
          New-Item -ItemType Directory -Path $Files.Destination -Verbose > $null
        }

        $ListManfiest = @{
          Name    = $FileSet.BaseName
          Path    = '.'
          Version = $PackageVersion
          Files   = $Files.Path
        }
        $src = Get-Manifest @ListManfiest

        $dst = Get-Manifest -Path $Files.Destination

        $changes = Get-ManifestChanges -SrcManifest $src -DstManifest $dst

        Sync-Folder -ManifestChanges $changes
      }

      if ($FileSet.ContainsKey('Folders')) {
        $Folders = @{
          Source      = (GeneratePath $FileSet.Folders['Path'])
          Destination = (GeneratePath $FileSet.Folders['Destination'])
        }

        # Need to ensure the Destination Path exists
        if (!(Test-Path -Path $Folders.Destination)) {
          New-Item -ItemType Directory -Path $Folders.Destination -Verbose > $null
        }

        $src = Get-Manifest -Path $Folders.Source
        # Directly set the package version for these files
        $src.Version = $PackageVersion

        $dst = Get-Manifest -Path $Folders.Destination

        $changes = Get-ManifestChanges -SrcManifest $src -DstManifest $dst

        Sync-Folder -ManifestChanges $changes
      }
    }
  }
  #endregion PSake Deploy

  #region PSake GitPull
  Task GitPull {
    Exec { git pull }
  }
  #endregion PSake GitPull

  #region PSake UpdateDotnetPackages
  Task UpdateDotnetPackages {
    Write-Host ('=== Update dotnet packages in [{0}] ' -f (Join-Path $PSScriptRoot 'collector*'))

    $projects = Get-ChildItem -Directory 'collector*'
    foreach ($project in $projects) {
      $items = dotnet list $project.Name package --outdated |
        rg '^\s.*> ([\w\.]+)\s\s+.*$' -r '$1'

    Write-Host ('--- Updating items [{0}:{1}]' -f $project, $items.Count)

    foreach ($item in $items) {
      Write-Host ('  + Package [{0}]' -f $item)

      dotnet add $project.Name package $item
    }
  }
}
#endregion PSake UpdateDotnetPackages
