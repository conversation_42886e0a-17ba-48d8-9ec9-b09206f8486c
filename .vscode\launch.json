{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": ".NET Core Launch (console) [Client]",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/collector.client/bin/Debug/net6.0/collector.client.dll",
      "args": [
        "register",
        "-s=localhost",
        "-d@=C:/Users/<USER>/Projects/labcollector/Scripts/register.json",
        "-o=C:/Users/<USER>/Projects/labcollector/Scripts/client.json"
      ],
      "cwd": "${workspaceFolder}/collector.client",
      "stopAtEntry": false,
      "console": "internalConsole"
    },
    {
      "name": ".NET Core Launch (console) [Server]",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/collector.server/bin/Debug/net6.0/collector.server.dll",
      "args": [],
      "cwd": "${workspaceFolder}/collector.server",
      "stopAtEntry": false,
      "console": "internalConsole"
    }
  ]
}