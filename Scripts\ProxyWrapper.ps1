<#
.Synopsis
  ProxyWrapper.ps1 provides a wrapper to provide SQL and AD updates for collector
#>
[CmdletBinding(DefaultParameterSetName = 'Help')]
param(
  # Help Switch
  [Parameter(ParameterSetName = 'Help')]
  [switch]
  $Help,
  <# SQL Parameters # >
  # SQL Mode
  [Parameter(ParameterSetName = 'SQL')]
  [switch]
  $SQL,
  # Schedule JSON file path
  [Parameter(ParameterSetName = 'SQL')]
  [string]
  $ScheduleJSON = "$PSScriptRoot\schedule.json",
  #>
  # LabData Mode
  [Parameter(ParameterSetName = 'LabData')]
  [switch]
  $LabData,
  # LabData CSV file
  [Parameter(ParameterSetName = 'LabData')]
  [string]
  $LabDataCSV = "$PSScriptRoot\LabData.csv",
  # LabData RoomName Filter
  [Parameter(ParameterSetName = 'LabData')]
  [string]
  $RoomFilter,
  # LabData JSON file
  [Parameter(ParameterSetName = 'LabData')]
  [string]
  $LabDataJSON = "$PSScriptRoot\labdata.json",
  # Output an Ansible Inventory file
  [Parameter(ParameterSetName = 'LabData', Mandatory = $false)]
  [switch]
  $Ansible,
  # Directory to output the Inventory file
  [Parameter(ParameterSetName = 'LabData', Mandatory = $false)]
  [string]$InventoryDir = "$PSScriptRoot\LabDataInventory",
  # Setup Mode
  [Parameter(ParameterSetName = 'Setup')]
  [switch]
  $Setup,
  # Credentials required for Setup Mode
  [Parameter(ParameterSetName = 'Setup')]
  [Parameter(ParameterSetName = 'Test', Mandatory)]
  [PSCredential]
  $Credentials,
  # Remove Mode
  [Parameter(ParameterSetName = 'Remove')]
  [Parameter(ParameterSetName = 'Test')]
  [switch]
  $Remove,
  # DSC Test Mode
  [Parameter(ParameterSetName = 'Test')]
  [switch]
  $Test,
  # General variables
  # Config file
  # [string]
  # $ConfigFile = "$PSScriptRoot\CollectorWrapper.cfg.psd1",
  # # Production vs Development Modes for Config file
  # [ValidateSet('None', 'Interurban', 'Lansdowne')]
  # [string]$Site,
  # For logging
  [Parameter()]
  [hashtable]
  # Local output directory; Hashtable: single key [Path]
  $OutputPath = @{ Path = 'C:\Scripts' },
  [hashtable]
  $LogFile = @{ Path = "$PSScriptRoot\ProxyWrapper-$(Get-Date -UFormat '%V').log" },
  [int]
  $LogsToKeep = 2,
  # For setup and timekeeping
  [string]
  $SqlStartingTime = '06:00:00',
  [string]
  $LdStartingTime = '06:10:00',
  # For calling the actual Collector Client
  [string]
  $CollectorCommand = "$PSScriptRoot/collector.client"
)

#Requires -Module ActiveDirectory
# , SqlServer
## Functions
function ShowHelp {
  param (
  )
  $Self = @{
    ScriptBlock = {
      help $PSCommandPath
    }
  }

  Invoke-Command @Self
}

function TaskTimeCheck {
  [CmdletBinding()]
  param (
    [Parameter()]
    [string]
    $TaskType,
    # StartTime
    [Parameter()]
    [string]
    $StartingTime,
    # Duration
    [Parameter()]
    [string]
    $Duration = '18:00:00'
  )
  $TaskName = $MainTitle + ' ' + $TaskType
  $StartTime = Get-Date $StartingTime
  $EndTime = $StartTime + $Duration
  $Now = Get-Date

  if (
    ($Now -ge $StartTime) -and
    ($Now -lt $EndTime)
  ) {
    Start-ScheduledTask $TaskName -ErrorAction SilentlyContinue
  } else {
    Stop-ScheduledTask $TaskName -ErrorAction SilentlyContinue
  }
}

function ConvertTaskToXML {
  param(
    [string]$Title,
    [string]$Type,
    [string]$Trigger,
    [string]$Parameters
  )
  $FileName = (Get-ChildItem -Path $PSCommandPath).Name
  <#
    <UserId>S-1-5-21-2059692052-2017471298-382417117-190919</UserId>
    <LogonType>Password</LogonType>
  #>
  $XML_Template = @"
<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
<RegistrationInfo>
<Date>$(Get-Date -Format s)</Date>
<Author>CAMOSUN\heyp</Author>
<URI>\$Title $Type</URI>
</RegistrationInfo>
<Triggers>
$Trigger
</Triggers>
<Principals>
  <Principal id="Author">
    <RunLevel>HighestAvailable</RunLevel>
  </Principal>
</Principals>
<Settings>
<MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
<DisallowStartIfOnBatteries>true</DisallowStartIfOnBatteries>
<StopIfGoingOnBatteries>true</StopIfGoingOnBatteries>
<AllowHardTerminate>true</AllowHardTerminate>
<StartWhenAvailable>false</StartWhenAvailable>
<RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
<IdleSettings>
  <StopOnIdleEnd>true</StopOnIdleEnd>
  <RestartOnIdle>false</RestartOnIdle>
</IdleSettings>
<AllowStartOnDemand>true</AllowStartOnDemand>
<Enabled>true</Enabled>
<Hidden>false</Hidden>
<RunOnlyIfIdle>false</RunOnlyIfIdle>
<DisallowStartOnRemoteAppSession>false</DisallowStartOnRemoteAppSession>
<UseUnifiedSchedulingEngine>true</UseUnifiedSchedulingEngine>
<WakeToRun>false</WakeToRun>
<ExecutionTimeLimit>PT20M</ExecutionTimeLimit>
<Priority>7</Priority>
</Settings>
<Actions Context="Author">
<Exec>
  <Command>powershell</Command>
  <Arguments>-NoLogo -NonInteractive -NoProfile -Command "&amp;{.\$FileName $Parameters}"</Arguments>
  <WorkingDirectory>$PSScriptRoot</WorkingDirectory>
</Exec>
</Actions>
</Task>
"@
  return $XML_Template
}

## Global Variables
$MainTitle = 'Collector Proxy Wrapper'
$TaskTypes = @(
  <# SQL Task # >
  @{
    Title     = $MainTitle
    Type      = 'SQL'
    Parameter = '-SQL'
    StartTime = $SqlStartingTime
    Trigger   = @"
  <CalendarTrigger>
    <Repetition>
      <Interval>PT1H</Interval>
      <Duration>PT18H</Duration>
      <StopAtDurationEnd>false</StopAtDurationEnd>
    </Repetition>
    <StartBoundary>$(Get-Date -Format s $SqlStartingTime)</StartBoundary>
    <Enabled>true</Enabled>
    <ScheduleByDay>
      <DaysInterval>1</DaysInterval>
    </ScheduleByDay>
  </CalendarTrigger>
"@
  }#>
  @{
    Title     = $MainTitle
    Type      = 'LabData'
    Parameter = '-LabData'
    StartTime = $LdStartingTime
    Trigger   = @"
  <CalendarTrigger>
    <Repetition>
      <Interval>PT30M</Interval>
      <Duration>PT18H</Duration>
      <StopAtDurationEnd>false</StopAtDurationEnd>
    </Repetition>
    <StartBoundary>$(Get-Date -Format s $LdStartingTime)</StartBoundary>
    <Enabled>true</Enabled>
    <ScheduleByDay>
      <DaysInterval>1</DaysInterval>
    </ScheduleByDay>
  </CalendarTrigger>
"@
  }
)

try {
  ## Start of Script
  Push-Location $PSScriptRoot
  # Get start time
  $StartTime = [datetime]::now

  if ($Test) {
    # Used for DSC
    $TestResults = @(
      foreach ($Task in $TaskTypes) {
        $ScheduledTask = @{
          TaskName = "$($Task.Title + ' ' + $Task.Type)"
          TaskPath = '\'
        }

        try {
          $FoundTask = Get-ScheduledTask @ScheduledTask -ErrorAction Stop

          # Return if they match
          # EG:  'camosun\agent_MyLabAccess' -match 'agent_MyLabAccess'
          $Credentials.UserName -match $FoundTask.Principal.UserId
          # if ($FoundTask.Principal.UserId -ne $Credentials.UserName) {
          #   $false
          # } else {
          #   $true
          # }
        } catch {
          $false
        }
      })
    return !($Remove -in $TestResults)
  }

  # Ensure Output Path exists
  if (!(Test-Path @OutputPath -PathType Container)) {
    New-Item @OutputPath -ItemType Directory -Force
  }

  # Handle Transcript
  $TranscriptFile = @{
    Path = (Join-Path @OutputPath -ChildPath 'ProxyWrapper-Transcript.log' )
  }

  if (Test-Path @TranscriptFile -PathType Leaf) {
    $today = Get-Date -Format 'yyyyMMdd'
    $LastLogged = Get-Date -Format 'yyyyMMdd' (Get-ChildItem @TranscriptFile).LastWriteTime

    if ($today -eq $LastLogged) {
      #If we're still on the same day, append
      Start-Transcript @TranscriptFile -Append
    } else {
      # Otherwise start over.
      Start-Transcript @TranscriptFile
    }
  } else {
    # Otherwise start over.
    Start-Transcript @TranscriptFile
  }

  # Log start time
  Write-Host "--- StartTime [$StartTime]"

  switch ($PSCmdlet.ParameterSetName) {
    'Help' {
      Write-Host "--- $_ [$([datetimeoffset]::Now)]"
      ShowHelp
    }
    <# SQL Handler Code # >
  'SQL' {

    # Lookup Hash for converting names to match lab Names
    $RoomNames = @{
      'CBA*119' = 'CBA119A'
      'CBA*124' = 'CBA124A'
      'CBA*144' = 'CBA144'
      'CBA*214' = 'CBA214'
      'CBA*287' = 'CBA287'
      'E*100'   = 'EWG100'
      'E*110'   = 'EWG110'
      'E*112'   = 'EWG112'
      'E*115'   = 'EWG115'
      'TEC*145' = 'TB145'
      'TEC*147' = 'TB147'
      'TEC*148' = 'TB148'
      'TEC*150' = 'TB150'
      'TEC*151' = 'TB151'
      'TEC*257' = 'TB257'
      'TEC*273' = 'TB273'
      'Y*307'   = 'Y307'
    }

    Write-Host "--- $_ [$([datetimeoffset]::Now)]"

    $sqlViewName = 'coll_cam.dbo.v_Computer_Lab_Requests'
    $Today = Get-Date -Format 'yyyy-MM-dd'

    $sqlcmd = @{
      Query          = @"
select *
from $sqlViewName
where CALS_DATE = '$Today'
order by ROOMS_ID, CALS_START_TIME
"@
      ServerInstance = 'colldbrpt'
      Database       = 'coll_cam'
    }

    $sqlData = Invoke-Sqlcmd @sqlcmd
    $SectionSchedules = if ($sqlData) {
      @(
        foreach ($Row in $sqlData) {
          # Handle RoomNames not in the lookup
          $RowRoomName = if (
            $null -eq $RoomNames[$Row.ROOMS_ID]
          ) {
            $Row.ROOMS_ID -replace '\*', ''
          } else {
            $RoomNames[$Row.ROOMS_ID]
          }
          # Return the Section Object
          [ordered]@{
            RoomName = $RowRoomName
            Start    = [DateTimeOffset]($Row.CALS_DATE + ' ' + $Row.CALS_START_TIME)
            End      = [DateTimeOffset]($Row.CALS_DATE + ' ' + $Row.CALS_END_TIME)
            Group    = @(
              $Row.SEC_SUBJECT,
              $Row.SEC_COURSE_NO,
              $Row.SEC_NO
            ) -join '-'
            Users    = @()
          }
        })
    }

    if ($SectionSchedules) {
      foreach ($Section in $SectionSchedules) {
        # Add the list of section users
        $Section['Users'] = Get-ADGroupMember -Identity $Section.Group |
          Where-Object objectClass -EQ 'user' |
          Select-Object -ExpandProperty SamAccountName
      }
    }

    $Schedule = [ordered]@{
      Type_GUID        = '9ab2482c-0548-49da-b39d-392e1a2fff29'
      Schedule_GUID    = '7338b54f-e84b-4d1c-8464-a902f5b8453d'
      CurrentDate      = [DateTimeOffset]::Now
      SectionSchedules = $SectionSchedules
    }

    $Schedule |
      ConvertTo-Json -Depth 10 -Compress |
      Out-File -Encoding utf8 -FilePath $ScheduleJSON -Force
    # @"
    & $CollectorCommand schedule -s="$($Config.ServerName)" -d@="$ScheduleJSON"
    # "@
  } #>
    'LabData' {
      Write-Host "--- $_ [$([datetimeoffset]::Now)]"

      $LabDataDir = @{ Path = "$PSScriptRoot\LabData\" }

      if (!(Test-Path @LabDataDir -PathType Container)) {
        New-Item @LabDataDir -ItemType Directory -Force
      }

      if (Test-Path -Path $LabDataCSV -PathType Leaf) {
        $csvData = Get-Content $LabDataCSV |
          ConvertFrom-Csv
      } else {
        Write-Host -Level Warn " --- $_ : no csv found at [$LabDataCSV]"
        exit
      }

      # Select Calculated Property
      $HostName = @{
        Name       = 'HostName'
        Expression = { $_.Name }
      }

      foreach ($lab_line in $csvData) {
        if ($lab_line.RoomName -notmatch $RoomFilter) {
          # Skip labs that don't match the RoomName filter
          continue
        }

        # Skip the lab if it's not enabled.
        # if ($lab_line.Enabled -eq $false){ continue }
        $AdDevices = if ($lab_line.DeviceGroup -eq 'Default') {
          @()
        } else {
          Get-ADGroupMember $lab_line.DeviceGroup -ErrorAction SilentlyContinue |
            Get-ADComputer |
            Sort-Object Name |
            Select-Object $HostName, DNSHostName, ObjectGUID
        }
        if ($AdDevices.Count -ne 0 ) {
          $Devices = @(
            foreach ($item in $AdDevices) {
              [ordered]@{
                HostName    = $item.HostName
                DNSHostName = $item.DNSHostName
                # "6bc3ef72-850e-40f0-b2fe-3297ac43482b"
                # Set a default guid string...
                #Client_GUID = "00000000-0000-0000-0000-000000000000"
                Client_GUID = $item.ObjectGUID
              }
            }
          )
          #$AdDevices | Select-Object HostName, Client_GUID, ObjectGUID
        } else {
          #Write-Host -Level Warn " --- LabData [$($lab_line.DeviceGroup)] has no devices."
          $Devices = @()
        }
        $Lab = [ordered]@{
          Type_GUID        = '620e8f12-6730-42eb-91e9-e6ed9ebfe6ce'
          Lab_GUID         = $lab_line.Lab_GUID
          RoomName         = $lab_line.RoomName
          Campus           = $lab_line.Campus
          Building         = $lab_line.Building
          Description      = $lab_line.Description
          HasSchedule      = $lab_line.HasSchedule
          IsAdminOnly      = $lab_line.IsAdminOnly
          IsStaffOnly      = $lab_line.IsStaffOnly
          Enabled          = $lab_line.Enabled
          LastUpdated      = [DateTimeOffset]::Now
          DeviceGroup      = $lab_line.DeviceGroup
          Devices          = @(
            $Devices
          )
          DefaultRDPgroups = @(
            $lab_line.DefaultRDPgroups
          )
        }

        $LabDataJSON = (Join-Path @LabDataDir -ChildPath "$($Lab.RoomName)-labdata.json")

        $Lab |
          ConvertTo-Json -Depth 10 -Compress |
          Out-File -Encoding utf8 -FilePath $LabDataJSON -Force
        Invoke-Expression @"
      $CollectorCommand lab -s="localhost" -d@="$LabDataJSON"
"@
        if ($Ansible) {
          # We need to build an inventory file...
          # If we have $Devices from above, use the $AdDevices list
          if ($Devices) {
            # Get the List of DNSHostNames
            $AnsibleInventory = $AdDevices | Select-Object DNSHostName
            # Build an Inventory base from that list
            $InventoryFileBase = @(
              foreach ($item in $AnsibleInventory) {
                '    {0}:' -f $item.DNSHostName
              })
          } else {
            # Otherwise, return an empty array
            $InventoryFileBase = @()
          }

          # Ansible stuff
          $InventoryHeader = @(
            "$($Lab.RoomName):"
            '  hosts:'
          )

          $Content = @{
            Value    = @(
              $InventoryHeader,
              $InventoryFileBase
            )
            Encoding = 'ascii'
            Path     = "$InventoryDir\$($Lab.RoomName).yml"
            Force    = $true
          }
          Set-Content @Content
        }
      }
    }

    'Setup' {
      Write-Host "--- $_ [$([datetimeoffset]::Now)]"
      foreach ($Task in $TaskTypes) {
        try {
          $ScheduledTask = @{
            Xml      = ConvertTaskToXML @Task
            TaskName = "$($Task.Title + ' ' + $Task.Type)"
            TaskPath = '\'
            User     = $Credentials.UserName
            Password = $Credentials.GetNetworkCredential().Password
            Verbose  = $true
          }
          #$xml = ConvertTaskToXML @Task
          #Register-ScheduledTask -Xml $xml -TaskName "$($Task.Title + ' ' + $Task.Type)" -Verbose
          if (Get-ScheduledTask -TaskName $ScheduledTask.TaskName -ErrorAction SilentlyContinue) {
            Write-Host "--- [$($ScheduledTask.TaskName)] already registered."
          } else {
            Register-ScheduledTask @ScheduledTask
            Write-Host "--- [$($ScheduledTask.TaskName)] now registered."
          }
          TaskTimeCheck -TaskType $Task.Type -StartingTime $Task.StartTime
        } catch {
          Write-Host "--- [$($Task.Title)] has an issue: < $_ >" -Level Warn
          continue
        }
      }
    }
    'Remove' {
      Write-Host "--- $_ [$([datetimeoffset]::Now)]"
      foreach ($Task in $TaskTypes) {
        try {
          $ScheduledTask = @{
            TaskName    = "$($Task.Title + ' ' + $Task.Type)"
            TaskPath    = '\'
            Verbose     = $true
            ErrorAction = 'SilentlyContinue'
          }
          if (Get-ScheduledTask -TaskName $ScheduledTask.TaskName -ErrorAction SilentlyContinue) {
            Unregister-ScheduledTask @ScheduledTask -Confirm:$false
            Write-Host "--- [$($ScheduledTask.TaskName)] now unregistered."
          } else {
            Write-Host "--- [$($ScheduledTask.TaskName)] not registered."
          }
        } catch {
          Write-Host "--- [$($Task.Title)] has an issue: < $_ >" -Level Warn
          continue
        }
      }
    }

    Default {}
  }
} catch {
  throw $_
} finally {
  if (!$Test) {
    Write-Host "--- StopTime [$([datetime]::Now)]"
    Stop-Transcript
  }
  Pop-Location
}
