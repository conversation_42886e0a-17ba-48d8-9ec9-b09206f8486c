# Installation Process

## 1. Install Prequisites

- After server has been joined to the domain
  use the Mr-Dsc Module to do the following

  ```powershell
  <#PS ADMIN#> DoStuff -single 'labcollectordev' <# ServerName #> -Tasks InstallNugetProvider, InstallBaseModules, SetWACDelegate -Verbose -Transcript
  ```

- This will setup some basic stuff needed for the rest of it.

## 2. Setup Modules

- RDP into Server and start an Admin Powershell session,
  Run:

  ```powershell
  \\naslan\DSCSources\LabCollector\Setup\SetupModules.ps1
  ```

- This will install the following

  - psake
  - MDBC
  - IISAdministration
  - xWebAdministration
  - NetworkingDsc
  - Microsoft.PowerShell.SecretManagement
  - Microsoft.PowerShell.SecretStore

- Diagram:

  ```plantuml
  @startuml SetupModules
  start
  partition #gold SetupModules.ps1 {
    :Run: SetupModules.ps1 (as Administrator)]
    :Config (File ScriptsDirectoryExists);
    :Config (PackageManagementSource PSGallery);
    repeat
      :Config (PackageManagement $Module);
    repeat while (ForEach $Module in $Modules)
    :Compile Configs>
    :Invoke DSC>
  }
  stop
  @enduml
  ```

## 3. Setup the Secrets (Per User running the install/setup scripts)

- **NOTE: Anything to do with the Secrets module will not show up in your command history, so keep note of the commands entered.**
- On first run this will prompt you for a password for the secret store.
  After the password has been set, the script will remove the secret store password for use in automation (which will prompt twice: once for the password and a second time to confirm).

  ```powershell
  \\naslan\DSCSources\LabCollector\Setup\SetupSecrets.ps1
  You will need to set a temporary password.
  Creating a new Microsoft.PowerShell.SecretStore vault. A password is required by the current store configuration.
  Enter password:
  ********
  Enter password again for verification:
  ********
  A password is no longer required for the local store configuration.
  To complete the change please provide the current password.
  Enter password:
  ********
  # This will prompt you for two accounts, agent_MyLabAccess and the *.camosun.ca GoDaddy PFX password.
  ```

- Diagram:

  ```plantuml
  @startuml SetupSecrets
  start
  partition #Salmon SetupSecrets.ps1 {
    :Run: SetupSecrets.ps1 (as "agent_MyLabAccess")]
    :Import-Modules

  - 'Microsoft.PowerShell.SecretManagement',
  - 'Microsoft.PowerShell.SecretStore'>

    :Set-SecretStoreConfig

  - This involves setting a temporary password>

    :Register-SecretVault>
    :Set-Secret
    -Name 'LabCollectorAgent'
    -Secret (Get-Credential -Message "LabCollectorAgent")>
  }
  stop
  @enduml
  ```

## 4. Setup the Listener Service

### a. Packages

- This will likely restart the box when it finishes

```powershell
$agent = Get-Secret 'LabCollectorAgent'

\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -Server -Site Dev <# 'Dev' or 'Prod' #> -Force -ProxyCredentials $agent -SetupTasks Packages
```

### b. Scripts

Should install the `collector.server` service and related components.

```powershell
$agent = Get-Secret 'LabCollectorAgent'

\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -Server -Site Dev <# Same as 4a #> -Force -ProxyCredentials $agent -SetupTasks Scripts
```

## 5. Setup the WebApp

### a. Features, Packages, Certificate

Pulls in the certificate PFX as described in `DeployServer.cfg.psd1`.

```powershell
\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -WebApp -ServerMode Dev <# Same as 4a #> -PfxCreds (Get-Secret 'GoDaddyWildCardPFX') -SetupTasks Features,Packages,Certificate -Force
```

### b. Repair the packages

- Not sure why this needs to happen, but IIS doesn't see the WebApp's `web.config` file properly without it.

```powershell
\\naslan\MSIDeploy\dotnet-hosting\dotnet-hosting-6.0.1-win.exe /repair /passive
\\naslan\MSIDeploy\dotnet-sdk\dotnet-sdk-6.0.101-win-x64.exe /repair /passive
```

### c. Scripts

```powershell
\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -WebApp -ServerMode Dev <# Same as 4a #> -PfxCreds (Get-Secret 'GoDaddyWildCardPFX') -SetupTasks Scripts -Force
```

## 6. Verify Install

At this point, there should be the following:

- `collector.server` service running
- `MongoDB` service running
- IIS site called `labcollectordev.camosun.ca` on http\https running on an AppPool called `LabCollectorAppPool`
- Scheduled task called `Collector Proxy Wrapper LabData` running as `agent_MyLabAccess`
- You should also be able to connect to this server via the IIS Manager

## 7. Troubleshooting

### 7a. If you see `The data is invalid. (Exception from HRESULT: 0x8007000D)`

```powershell
# CONTEXT #
--- Invoke DSC [SetupCollectorWebApp]
The data is invalid. (Exception from HRESULT: 0x8007000D)
    + CategoryInfo          : NotSpecified: (:) [], CimException
    + FullyQualifiedErrorId : System.Runtime.InteropServices.COMException,Microsoft.IIs.PowerShell.Provider.GetConfigurationPropertyCommand
    + PSComputerName        : localhost

PowerShell DSC resource MSFT_xWebSite  failed to execute Set-TargetResource functionality with error message: The data is invalid. (Exception from HRESULT: 0x8007000D)
    + CategoryInfo          : InvalidOperation: (:) [], CimException
    + FullyQualifiedErrorId : ProviderOperationExecutionFailure
    + PSComputerName        : localhost

The SendConfigurationApply function did not succeed.
    + CategoryInfo          : NotSpecified: (root/Microsoft/...gurationManager:String) [], CimException
    + FullyQualifiedErrorId : MI RESULT 1
    + PSComputerName        : localhost
```

This is likely due to the `dotnet-hosting` and `dotnet-sdk` not being seen by IIS, please re-run Step **5b** and then **5c**.

### 7b. Checking the Listener Log files

If you see lines like:

```log
2022-01-26 11:55:24.562 -08:00 [INF] [Worker : 3]  Received < {"Type_GUID":"2838835a-6b58-4d7e-b83d-7401aca79aee","HostName":"ILC340-SPARE4","SerialNumber":"DTVNLAA0076250820F3000","IsRegistering":true}:False >
2022-01-26 11:55:24.563 -08:00 [INF] [Worker : 3]  Processing Registration < DTVNLAA0076250820F3000 >
2022-01-26 11:55:24.563 -08:00 [INF] [ProccessingRegistration] Processing: [DTVNLAA0076250820F3000]
2022-01-26 11:55:24.564 -08:00 [INF] [ProccessingRegistration] Client does not exist. It needs to be in a lab group before it can be registered.
2022-01-26 11:55:24.564 -08:00 [INF] [ProccessingRegistration] Returning empty guid [ILC340-SPARE4 : DTVNLAA0076250820F3000 : 00000000-0000-0000-0000-000000000000]
```

Use ripgrep (or similar regex tool) to select all of the similar lines:

```powershell
❯ cd \\labcollector\c$\Scripts\LabCollector\Server\Logs\Listener
❯ rg -uu '^.*Returning empty guid \[(.*):00000000-0000-0000-0000-000000000000:(.*)\]' -r '{\"HostName\":\"$1\",\"SerialNumber\":\"$2\"}' -N -I | select -Unique | sort | convertfrom-json
#...
HostName    SerialNumber
--------    ------------
CBA159-24T  PS00847065532000A80100
CBA202-21   UDP01AA387733000B30100
CTEI124B-05 DTVT5AA00F124036AE9600
TB147-03    MJ02VGSX
TB148-18    MJ02VGSB
Y107-03     DTVVEAA00913705B889600
Y107-05     DTVVEAA00913705B919600
Y307-07     MJ0AQJPN
#...
```

This should display any system that fails to register, likely due the GPO applying to a system not in a registered lab group.

### 7c. New Client Hardware (eg. Serial Number Changed)

TODO: The process to clear old serial numbers/client data and re-registering.

Use ripgrep (or similar) to search for unregister requests:

```powershell
  # From the "\\labcollector\c$\Scripts\LabCollector\Server" directory
  ❯ rg -uu '^.*\[ProccessingRegistration\] Please unregister <(.*)>' -r '$1' -N -I |select -Unique| convertfrom-json

  Type_GUID                            HostName    SerialNumber                        IsRegistering
  ---------                            --------    ------------                        -------------
  2838835a-6b58-4d7e-b83d-7401aca79aee PAUL-TESTVM 75f1b357-841f-49668f0c-9dfa4f0db4a1         False

  # Build a list of objects
  ❯ $UnRegister = rg -uu '^.*\[ProccessingRegistration\] Please unregister <(.*)>' -r '$1' -N -I |select -Unique| convertfrom-json
  # Write a bunch of JSON files out
  ❯ foreach($hn in $UnRegister){ $hn | ConvertTo-Json -Compress | Out-File -Encoding utf8 -FilePath ".\unregister\$($hn.HostName).unregister.json" -Force }
  # UnRegister the clients
  ❯ foreach($hn in $UnRegister) .\collector.client.exe r -d@=".\unregister\PAUL-TESTVM.unregister.json" -s="labcollectordev.intra.camosun.bc.ca" -o=".\unregister\PAUL-TESTVM.client.json"

```

### 7d. Secret Module issues

There seems to be some [issues](https://github.com/PowerShell/SecretStore/pull/86) concerning user names.

Typical error looks like:

```powershell
> Get-Secret 'GoDaddyWildCardPFX'
Get-Secret : Exception calling "GetInstance" with "0" argument(s): "Padding is invalid and cannot be removed."
At line:1 char:1
+ Get-Secret 'GoDaddyWildCardPFX'
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
+ CategoryInfo          : NotSpecified: (:) [Get-Secret], MethodInvocationException
+ FullyQualifiedErrorId : CryptographicException,Microsoft.PowerShell.SecretManagement.GetSecretCommand
Get-Secret : Exception calling "GetInstance" with "0" argument(s): "Padding is invalid and cannot be removed."
At line:1 char:1
+ Get-Secret 'GoDaddyWildCardPFX'
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
+ CategoryInfo          : NotSpecified: (:) [Get-Secret], MethodInvocationException
+ FullyQualifiedErrorId : CryptographicException,Microsoft.PowerShell.SecretManagement.GetSecretCommand
Get-Secret : The secret GoDaddyWildCardPFX was not found.
At line:1 char:1
+ Get-Secret 'GoDaddyWildCardPFX'
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
+ CategoryInfo          : ObjectNotFound: (Microsoft.Power...etSecretCommand:GetSecretCommand) [Get-Secret], ItemNotFoundException
+ FullyQualifiedErrorId : GetSecretNotFound,Microsoft.PowerShell.SecretManagement.GetSecretCommand
```

This seems to happen when your username changes, update the modules (See [Setup Modules](#2-setup-modules)) then you will need to reset the Secret Store:

```powershell
> Reset-SecretStore
WARNING: !!This operation completely removes all SecretStore module secrets and resets configuration settings to new values!!

Reset SecretStore
Are you sure you want to erase all secrets in SecretStore and reset configuration settings to default?
[Y] Yes  [A] Yes to All  [N] No  [L] No to All  [S] Suspend  [?] Help (default is "N"): Y
Creating a new Microsoft.PowerShell.SecretStore vault. A password is required by the current store configuration.
Enter password:
********
Enter password again for verification:
********
```

You will then need to re-initialize the secrets (see [Setup the secrets](#3-setup-the-secrets-per-user-running-the-installsetup-scripts)).

## 8. Updates

```powershell
foreach ($Module in (Get-InstalledModule)) {
  Get-InstalledModule -Name $Module.Name -AllVersions |
    Where-Object Version -LT $Module.Version |
    Uninstall-Module -Verbose -Force
}
```

### 8a. Listener Updates

- You must stop the `collector.server` service before updates can be applied

```powershell
Stop-Service collector.server

# Get the agent creds
$agent = Get-Secret 'LabCollectorAgent'

# Run the deploy script in remove mode first, then normal mode to update otherwise the DSC will complain about the `collector.server` service.
\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -Server -Site Prod -ProxyCredentials $agent -SetupTasks Packages,Scripts -Force -Remove
\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -Server -Site Prod -ProxyCredentials $agent -SetupTasks Packages,Scripts -Force
```

### 8b. WebApp Updates

- You must stop the webappPool before any updates can be applied

```powershell
# Get Credentials for the PFX
$pfxCreds = Get-Secret 'GoDaddyWildCardPFX'
# Stop the webAppPool
Get-IISAppPool -Name 'LabCollectorAppPool' | Stop-WebAppPool

# Run the Setup script again
\\naslan\DSCSources\LabCollector\Setup\DeployServer.ps1 -WebApp -ServerMode Prod -PfxCreds $pfxCreds -SetupTasks Packages, Certificate, Scripts -Force

# The appPool and the website should be running again.
Get-IISAppPool -Name 'LabCollectorAppPool'

# Name                 Status       CLR Ver  Pipeline Mode  Start Mode
# ----                 ------       -------  -------------  ----------
# LabCollectorAppPool  Started      v4.0     Integrated     OnDemand

Get-Website -Name 'LabCollectorWebApp'

# Name             ID   State      Physical Path                  Bindings
# ----             --   -----      -------------                  --------
# LabCollectorWebA 1    Started    C:\inetpub\LabCollectorWebAppR http *:80:labcollectordev.camosun.ca
# pp                               oot\                           https *:443:labcollectordev.camosun.ca sslFlags=0
```

### Dev vs Prod

The `psakefile.ps1` can be reconfigured to push to a different directory by adjusting the `$ProjectName` property. And additionaly, this location will need to match in the deploy scripts.

For the client:

```powershell
# Lines 120 -> 124
$SourceName = if ($Site -in 'Dev', 'None') {
  'LabCollectorDev'
} else {
  'LabCollector'
}
```

For the server:

```powershell
$SourceName = if (
  ($ServerMode -eq 'Dev') -or
  ($Site -in 'Dev','None')
) {
  'LabCollectorDev'
} else {
  'LabCollector'
}
```

## Notes

- When deploying to `\\naslan\dscsources\` you may have to stop sharing with `Domain Computers` and kill any open file sessions.
  I'm going to have to come up with a workaround for this, likely using a pre-calculated filehash `*.psd1` or `*.json` file for each directory.
- I've added a `PackageManifest.psm1` module to help with this issue,
  however, DSC seems to need checksums anyways. So this will help reduce the number of files needing checksums.
- Might have to add Authenticated Users to the WebRoot director (eg. `c:\inetpub\LabCollectorWebAppRoot`)
- If you run into an issue where the DSC fails because `the request size exceeded the configured MaxEnvelopeSize` then you need to `Set-ItemProperty -Name maxEnvelopeSize -Path HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WSMAN\Client -Value 8192 -Force -Verbose`
