@{
  # Webserver root share\directory
  ServerRoot = '\\softtsrv\Sites'
  Labs       = @(
    <# None, None None # >
    @{
      GUID           = '17a8b301-d86f-43eb-a5e8-9181400ac421'
      Department     = 'None, None'
      LabNames       = @('None')
      IsGroup        = $false
      SamAccountName = 'Default'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Instructor Stations Prod # >
    @{
      GUID           = '28b63c6c-c048-4e6d-8a5e-6eff0d013014'
      Department     = 'Instructor Workstations' # Descriptive Name for Lab/Department
      LabNames       = @('Staff') # The name of the lab for Single page or Sub page
      IsGroup        = $true # True: Single page for all labs; False: one page per Lab Name
      SamAccountName = 'InstructorLabWorkstations' # Or SamAccountName for lab machine Security group
      OutputFile     = '\\softtsrv\Sites\MyLabAccess.camosun.bc.ca\wwwroot\Staff\index.html' # Dedicated output file, if IsGroup = $true. Otherwise generated dynamically.
      SiteApp        = 'MyLabAccess.camosun.bc.ca' # Site application on WebServer, used if IsGroup = $false
      SiteRoot       = 'wwwroot\Staff' # Default site root ==> \\$ServerRoot\$SiteApp\$SiteRoot\$LabNames\
    }#>
    <# TestStations #>
    @{
      GUID           = '792a33fe-3f20-4660-aebe-34caa91a2fe7'
      Department     = 'Test Stations'
      LabNames       = @('TestStations')
      IsGroup        = $true
      SamAccountName = 'TestStations'
      OutputFile     = '\\softtsrv\Sites\MyLabAccess.camosun.bc.ca\wwwroot\TestStations\index.html'
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\TestStations'
      UseMongoDB     = $true
    }#>
    <# Lansdowne, Ewing Building  EWG102 #>
    @{
      GUID           = 'e380f3eb-7f20-4b86-92b3-8b1b2412b682'
      Department     = 'Lansdowne, Ewing Building '
      LabNames       = @('EWG102')
      IsGroup        = $false
      SamAccountName = 'EWG102_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\TestStations'
      UseMongoDB     = $true
    }#>
  )
}
